<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_initiating_workflow')"
    width="80%"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <div class="steps-wrap">
      <!-- <div :class="['step-item',currentStep==1?'is-current':'']">
                <div class="step-num">1</div>
                <div class="step-title">{{$t('txt_enter_init_work')}}</div>
                <div><i class="jwi-arrow-right"></i></div>
            </div> -->
      <div :class="['step-item', currentStep == 2 ? 'is-current' : '']">
        <div class="step-num">1</div>
        <div class="step-title">{{ $t("txt_select_work") }}</div>
        <div>
          <i class="jwi-arrow-right"></i>
        </div>
      </div>
      <div :class="['step-item', currentStep == 3 ? 'is-current' : '']">
        <div class="step-num">2</div>
        <div class="step-title">{{ $t("txt_select_temp") }}</div>
      </div>
    </div>
    <div class="step-body" v-show="currentStep == 1">
      <a-form-model
        ref="addForm"
        class="form-container"
        layout="vertical"
        :model="form"
        :rules="rules"
      >
        <a-row :gutter="10">
          <a-col :span="12" v-if="pageCode === 'myProcess'">
            <a-form-model-item
              :label="$t('txt_product_rq')"
              prop="productContainer"
            >
              <a-select
                v-model.trim="form.productContainer"
                :placeholder="$t('msg_select')"
                @focus="getContainerList"
                @change="onChangeConList"
              >
                <a-select-option
                  v-for="val in conOpts"
                  :key="val.oid"
                  :value="val.oid"
                >
                  {{ val.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$t('txt_from_type')"
              prop="modelDefinition"
            >
              <a-select
                v-model.trim="form.modelDefinition"
                :placeholder="$t('msg_select')"
                :loading="modelDefinitionLoading"
              >
                <a-select-option
                  v-for="val in typeOpts"
                  :key="val.oid"
                  :value="val.name"
                >
                  {{ val.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <jw-layout-builder
        v-if="visible && form.modelDefinition"
        ref="ref_appBuilder"
        type="Model"
        :layoutName="'create'"
        :modelName="form.modelDefinition"
        :instanceData="instanceData"
      >
      </jw-layout-builder>
    </div>
    <div class="step-body" v-show="currentStep == 2">
      <!-- 历史选择工作流对象 -->
      <!-- <jw-search-engine-content
                ref="addObjectContent"
                :pageCode="pageCode"
                :onlySearchObject="true"
                :model-list="modelList"
            /> -->
      <select-table-data
        :selectedRows.sync="selectedRows"
        :defaultTableList="defaultTableList"
      />
    </div>
    <div class="step-body step-body3" v-show="currentStep == 3">
      <div class="step3-left">
        <a-input-search
          v-model.trim="searchKey"
          class="search-input"
          allow-clear
          :placeholder="$t('search_text')"
          @search="onSearch"
        />
        <div class="radio-wrap">
          <a-radio-group v-model.trim="selectWorkflow" @change="onChangeRadio">
            <a-radio
              v-for="item in workflowListData"
              :key="item.key"
              :value="item"
            >
              {{ item.name }}
            </a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="step3-right">
        <div class="right-title">{{ selectWorkflow.name }}</div>
        <div class="right-tip">{{ selectWorkflow.metaInfo }}</div>
        <div class="right-process-img">
          <img :src="workflowImageUrl" alt="" />
        </div>
        <!-- <div class="right-temp-wrap">
          <div>{{ $t("txt_team_temp") }}</div>
          <a-select v-model.trim="teamTemp" :placeholder="$t('msg_select')" show-search :filter-option="filterOption" allowClear @change="getRoleWithUser">
            <a-select-option v-for="val in teamTempOpts" :key="val.oid" :value="val.oid">
              {{ val.name }}
            </a-select-option>
          </a-select>
        </div> -->
        <a-form-model ref="additionalForm" :model="stepsInfo.additionaltranslation" >
          <div v-if="selectWorkflow.key === 'cn_jwis_wjwf'">

            <a-form-model-item label="文件外发类型" prop="outgoingType">
              <a-select
                  v-model="stepsInfo.additionaltranslation.outgoingType"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="电子版本">电子版本</a-select-option>
                <a-select-option value="纸质版本">纸质版本</a-select-option>
              </a-select>
            </a-form-model-item>

            <a-form-model-item label="文件外发数量" prop="outgoingNumber">
              <a-input-number v-model="stepsInfo.additionaltranslation.outgoingNumber" default-value="1" :min="1" :max="200000" class="form-item"/>
            </a-form-model-item>

            <a-form-model-item label="外发单位" prop="outgoingCompany">
              <a-input v-model="stepsInfo.additionaltranslation.outgoingCompany" class="form-item"></a-input>
            </a-form-model-item>
          </div>
        </a-form-model>
        <a-form-model ref="additionalForm" :model="stepsInfo.additionaltranslation" >
<!--          <div v-if="selectWorkflow.key === 'cn_jwis_partworkflow'">-->
          <div v-if="finalShouldShowDiv">

            <a-form-model-item label="更改类型" prop="changeType">
              <a-select
                  v-model="stepsInfo.additionaltranslation.changeType"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="设计更改">设计更改</a-select-option>
                <a-select-option value="申请时填写错误">申请时填写错误</a-select-option>
                <a-select-option value="实物来料与系统内已有档案不符">实物来料与系统内已有档案不符</a-select-option>
              </a-select>
            </a-form-model-item>

            <a-form-model-item label="更改原因" prop="changeReason">
              <a-input v-model="stepsInfo.additionaltranslation.changeReason"
                       class="form-item"
                       placeholder="请输入更改原因"
              ></a-input>
            </a-form-model-item>

            <a-form-model-item label="更改方案" prop="changePlan">
              <a-input
                  v-model="stepsInfo.additionaltranslation.changePlan"
                  class="form-item"
                  placeholder="请输入更改方案"
              />
            </a-form-model-item>
            <a-form-model-item label="更改受影响对象" prop="affectedObjects">
              <a-input
                  v-model="stepsInfo.additionaltranslation.affectedObjects"
                  class="form-item"
                  placeholder="请输入更改受影响对象"
              />
            </a-form-model-item>

            <a-form-model-item label="更改影响分析" prop="impactAnalysis">
              <a-input
                  v-model="stepsInfo.additionaltranslation.impactAnalysis"
                  class="form-item"
                  placeholder="请输入更改影响分析"
              />
            </a-form-model-item>

            <a-form-model-item label="上传更改附件" prop="changeAttachments">
              <a-upload
                  v-model:file-list="stepsInfo.additionaltranslation.changeAttachments"
                  list-type="text"
                  :max-count="5"
                  :before-upload="beforeUpload"
              >
                <a-button icon="upload">点击上传</a-button>
              </a-upload>

            </a-form-model-item>

          </div>
        </a-form-model>
        <a-form-model :model="stepsInfo.additionaltranslation" >
          <div v-if="selectWorkflow.key === 'cn_jwis_docbg'">
            <a-form-model-item
                label="是否函审"
                prop="isReview"
                :rules="[{ required: true, message: '请选择是否函审' }]"
            >
              <a-select
                  v-model="stepsInfo.additionaltranslation.isReview"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="是">是</a-select-option>
                <a-select-option value="否">否</a-select-option>
              </a-select>
            </a-form-model-item>

            <a-form-model-item
                label="是否外发（包括自研单机方）"
                prop="isOutgoing"
                :rules="[{ required: true, message: '请选择是否外发' }]"
            >
              <a-select
                  v-model="stepsInfo.additionaltranslation.isOutgoing"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="是">是</a-select-option>
                <a-select-option value="否">否</a-select-option>
              </a-select>
            </a-form-model-item>
          </div>
        </a-form-model>
        <a-form-model :model="stepsInfo.additionaltranslation" >
          <div v-if="selectWorkflow.key === 'cn_jwis_qr'">
            <a-form-model-item label="更改内容说明" prop="changeDesc">
              <a-input v-model="stepsInfo.additionaltranslation.changeDesc"
                       class="form-item"
                       placeholder="请输入更改内容说明"
              ></a-input>
            </a-form-model-item>
          </div>
        </a-form-model>

<!--        <a-form-model ref="additionalForm" :model="stepsInfo.additionaltranslation" >
          <div v-if="selectWorkflow.key === 'cn_jwis_partworkflow'">
            <a-form-model-item
                label="是否外发（包括自研单机方）"
                prop="isOutgoing"
                :rules="[{ required: true, message: '请选择是否外发' }]"
            >
              <a-select
                  v-model="stepsInfo.additionaltranslation.isOutgoing"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="是">是</a-select-option>
                <a-select-option value="否">否</a-select-option>
              </a-select>
            </a-form-model-item>

&lt;!&ndash;            <a-form-model-item label="更改类型" prop="changeType">
              <a-select
                  v-model="stepsInfo.additionaltranslation.changeType"
                  placeholder="请选择"
                  class="form-item"
              >
                <a-select-option value="设计更改">设计更改</a-select-option>
                <a-select-option value="申请时填写错误">申请时填写错误</a-select-option>
                <a-select-option value="实物来料与系统内已有档案不符">实物来料与系统内已有档案不符</a-select-option>
              </a-select>
            </a-form-model-item>&ndash;&gt;

          </div>
        </a-form-model>-->

        <div class="right-handler-wrap" v-show="variableList.length > 0">
          <div
            class="handler-item"
            v-for="item in variableList"
            :key="item.name"
          >
            <div class="item-head">
              {{ item.displayName }}
              <i class="jwi-iconrefresh"></i>
            </div>
            <div class="item-body">
              <div class="handler-add-btn" @click="onAddUser(item)">
                <i class="jwi-iconuser-add"></i>
              </div>
              <div class="handlers" v-show="!!item.users.length">
                <div
                  class="handler"
                  v-for="val in item.users"
                  :key="val.account"
                >
                  <i
                    class="jwi-iconclose-circle-full close-icon"
                    @click="onDeleteUser(item.users, val)"
                  ></i>
                  <div class="avatar">
                    <img v-if="val.avatar" :src="val.avatar" alt="" />
                    <a-avatar v-else>{{ val.name | filterName }}</a-avatar>
                  </div>
                  <div>{{ val.name }}（{{ val.account }}）</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="foot-btns">
      <a-button v-if="currentStep == 1" type="primary" @click="onNextTo2"
        >{{ $t("btn_next_step") }}
      </a-button>

      <!--      <a-button v-if="currentStep==2"
                      class="form-foot-btn"
                      @click="onPrevious(1)"
                      :disabled="pageCode === 'objectProcess'">{{$t('btn_pre_step')}} </a-button>-->
      <a-button
        v-if="currentStep == 2"
        class="form-foot-btn"
        type="primary"
        @click="onNextTo3"
        >{{ $t("btn_next_step") }}
      </a-button>

      <a-button
        v-if="currentStep == 3"
        class="form-foot-btn"
        @click="onPrevious(2)"
        :disabled="pageCode === 'objectProcess'"
        >{{ $t("btn_pre_step") }}
      </a-button>
      <a-button
        v-if="currentStep == 3 && !detailInfo.oid"
        :loading="saveLoading"
        class="form-foot-btn"
        @click="onSave('save')"
        >{{ $t("txt_save_drafts") }}</a-button
      >
      <a-button
        v-if="currentStep == 3 && !detailInfo.oid"
        :loading="startLoading"
        class="form-foot-btn"
        type="primary"
        @click="onStart('start')"
        >{{ $t("txt_start_process") }}</a-button
      >

      <a-button
        v-if="currentStep == 3 && detailInfo.oid"
        :loading="updateLoading"
        class="form-foot-btn"
        @click="onSave('update')"
        :disabled="pageCode === 'objectProcess'"
        >{{ $t("txt_save_drafts") }}</a-button
      >
      <a-button
        v-if="currentStep == 3 && detailInfo.oid"
        :loading="updateStartLoading"
        class="form-foot-btn"
        type="primary"
        @click="onStart('updateStart')"
        >{{ $t("txt_start_process") }}</a-button
      >

      <a-button class="form-foot-btn" @click="onCancel"
        >{{ $t("btn_cancel") }}
      </a-button>
    </div>
    <userModalV3
      v-if="['cn_jwis_wlbmsq','cn_jwis_wjwf', 'cn_jwis_jswdfb','cn_jwis_partworkflow','cn_yhht_wlstop'].includes(selectWorkflow.key)"
      ref="user-modal"
      :isCheckbox="ischeckBox"
    />
    <jw-user-modal-v2 v-else ref="user-modal" :isCheckbox="ischeckBox" />
  </a-modal>
</template>

<script>
import axios from "axios";
import { jwLayoutBuilder, jwUserModalV2, jwSearchEngineContent } from "jw_frame"
import userModalV3 from "components/user-modal-v3"
import { findChangeInfoApi } from "/apis/change/index"
import { getCookie } from "jw_utils/cookie"
import ModelFactory from "jw_apis/model-factory"
import SelectTableData from "./select-table-data.vue"

// 校验BOM变更流程中审批内容
const verifyContent = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/verifyContent`,
  method: 'post',
});


// 获取相关对象
const relationObjsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
  method: "get"
});

const relationSearchModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
  method: "post"
});

// 产品容器列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
  method: "post",
})

// 获取申请单类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/flapSubTree`,
  method: "get",
})

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post",
})

// 查询已部署的最新流程模板
const fetchDeployed = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/workflow-micro/workflow/model/deployed/latest`,
  method: "post",
})

// 获取流程图
const fetchProcessImage = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey`,
  method: "get",
})

// 获取团队
const fetchTeam = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindAllTeam`,
  method: "get",
})

// 获取需要配置的角色
const fetchVariables = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/start/variables`,
  method: "get",
})

// 获取角色对应的用户
const fetchRoleWithUser = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/search/roleWithUser`,
  method: "get",
})

// 保存草稿
const createProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/create`,
  method: "post",
})

// 创建并启动流程
const createThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/customer/ding/task/createSendTask`,
  method: "post",
})

// 更新流程单
const updateProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/updateAll`,
  method: "post",
})

// 更新并启动流程
const updateThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/updateAllThenStart`,
  method: "post",
})

// 获取流程单详情
const fetchDetails = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/findDetail`,
  method: "get",
})

/**
 * 获取流程模板
 */
const findWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findWorkflow`,
  method: "get",
})

const preSignedPutUrl = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/preSignedPutUrl`,
  method: "get",
});

const FileMetadataApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/createFileMetadata`,
  method: "post",
});

export default {
  name: "addworkflowModal",
  components: {
    jwLayoutBuilder,
    jwSearchEngineContent,
    jwUserModalV2,
    SelectTableData,
    userModalV3,
  },
  props: ["visible", "pageCode", "detailInfo", "objectDetailsData", "typeList"],
  data() {
    this._fileParams = {};
    this._uploadFile = {};
    return {
      fileList: [],
      stepsInfo: {
        additionaltranslation: {
          isReview: null,
          isOutgoing:null,
          changeType: "",         // 更改类型
          changeDesc: "",         // 更改类型
          changeReason: "",       // 更改原因
          changePlan: "",          // 更改方案
          affectedObjects: "",     // 更改受影响对象
          impactAnalysis: "",      // 更改影响分析
          changeAttachments: [],    // 上传更改附件
          outgoingType:"电子版本",
          outgoingNumber:1,
          outgoingCompany:"",
        },
      },
      currentStep: 0,
      form: {
        productContainer: undefined,
        modelDefinition: "ProcessOrder",
      },
      rules: {
        isOutgoing: [{ required: true, message: "请选择是否外发", trigger: "blur" }],
        changeType: [{ required: true, message: "请选择更改类型", trigger: "blur" }],
        changeReason: [{ required: true, message: "请输入更改原因", trigger: "blur" }],
        changePlan: [{ required: true, message: "请输入更改方案", trigger: "blur" }],
        affectedObjects: [{ required: true, message: "请输入更改受影响对象", trigger: "blur" }],
        impactAnalysis: [{ required: true, message: "请输入更改影响分析", trigger: "blur" }],
        changeAttachments: [
          {
            required: true,
            message: "请上传至少一个附件",
            validator: (rule, value) => {
              if (value.length > 0) return Promise.resolve();
              return Promise.reject("请上传至少一个附件");
            }
          }
        ],
        productContainer: [
          {
            required: true,
            message: this.$t("txt_please_container"),
            trigger: "change",
          },
        ],
        modelDefinition: [
          {
            required: true,
            message: this.$t("txt_select_from_type"),
            trigger: "change",
          },
        ],
      },
      conOpts: [],
      containerInfo: {},
      typeOpts: [],
      modelList: [
        {
          name: this.$t("txt_part"),
          code: "PartIteration",
        },
        {
          name: this.$t("txt_document"),
          code: "DocumentIteration",
        },
        {
          name: "MCAD",
          code: "MCADIteration",
        },
        {
          name: "ECAD",
          code: "ECADIteration",
        },
        {
          name: this.$t("txt_baseline"),
          code: "Baseline",
        },
      ],
      instanceData: {},
      searchKey: "",
      workflowList: [],
      selectWorkflow: {},
      workflowImageUrl: "",
      onlyWorkflowModelId: "",
      teamTemp: undefined,
      teamTempOpts: [],
      variableList: [],
      step1Params: {},
      submitParams: {},
      ischeckBox: true,
      saveLoading: false,
      startLoading: false,
      updateLoading: false,
      updateStartLoading: false,
      modelDefinitionLoading: false,
      //选择项
      selectedRows: [],
      //默认选项table
      defaultTableList: [],
      shouldShowDivAsyncResult: false, // 新增：异步校验结果
    }
  },
  created() {
    // 动态初始化字段
    this.resetAdditionalTranslation();
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (this.pageCode === "objectProcess") {
            this.form.modelDefinition = "ProcessOrder"
            this.step1Params.name = this.detailInfo.name
            this.findWorkflow(
              this.detailInfo.modelDefinition || this.detailInfo.type
            )
          } else if (
            this.pageCode === "processManage" &&
            this.typeList &&
            this.typeList.size === 1
          ) {
            this.findWorkflow(Array.from(this.typeList)[0])
          }
          if (this.detailInfo && this.detailInfo.oid) {
            this.currentStep = 3
          } else {
            this.currentStep = 2
          }
        } else {
          // 清理默认值
          this.defaultTableList = []
          this.onlyWorkflowModelId = ""
        }
      },
      immediate: true,
    },
    currentStep: {
      async handler(val) {
        if (val == 1) {
          this.getContainerList()
          this.getSubModel()
        } else if (val == 2) {
          this.$nextTick(() => {
            // this.$refs.addObjectContent.init();
          })
        } else if (val == 3) {
          await this.getDeployed()
          this.getTeam()
          if (
            this.detailInfo &&
            this.detailInfo.oid &&
            this.pageCode !== "objectProcess"
          ) {
            this.getDetails()
          }
        }
      },
      immediate: true,
    },
    selectedRows: {
      handler() {
        this.handleDivCheck();
      },
      deep: true
    },
    selectWorkflow: {
      handler() {
        this.handleDivCheck();
      },
      deep: true
    },
  },
  computed: {
    workflowListData() {
      if (this.onlyWorkflowModelId) {
        let choosearr = this.workflowList.filter(
          (p) => p.id == this.onlyWorkflowModelId
        )
        if (choosearr.length > 0) {
          return choosearr
        } else {
          console.error(
            "未找到模型id,组织管理中优先级比系统管理中绑定的模型优先级高",
            this.onlyWorkflowModelId
          )
          return this.workflowList
        }
      } else {
        return this.workflowList || []
      }
    },
    shouldShowDiv() {
      const key = this.selectWorkflow.key; // 获取当前的 key
      const objects = this.getObjects(); // 获取需要校验的对象列表

      if (!Array.isArray(objects)) {
        return false; // 如果 objects 不是数组，直接返回 false
      }

      // 判断条件：objects 中是否有非 A01 的对象，并且 key === "cn_jwis_partworkflow"
      const hasInvalidDisplayVersion = objects.some(obj => {
        // 确保 obj.displayVersion 存在并且以 "A01" 开头
        return obj.displayVersion && !obj.displayVersion.startsWith("A01");
      });
      // console.log("是否应该显示" + hasInvalidDisplayVersion);
      return hasInvalidDisplayVersion && key === "cn_jwis_partworkflow";
    },
    finalShouldShowDiv() {
      // 新增：最终显示条件
      return this.shouldShowDiv && this.shouldShowDivAsyncResult;
    },
  },
  filters: {
    filterName: function (name) {
      if (!name) {
        return name
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name)
      let showName = ""
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2)
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase()
      }
      return showName
    },
  },
  mounted() {
    // this.getSubModel()
  },
  methods: {
    resetAdditionalTranslation() {
      this.stepsInfo.additionaltranslation = {
        changeType: "",         // 更改类型
        changeReason: "",       // 更改原因
        changePlan: "",         // 更改方案
        affectedObjects: "",    // 更改受影响对象
        impactAnalysis: "",     // 更改影响分析
        changeAttachments: [],  // 上传更改附件
      };
    },
    //查询变更数据
    findAutoRoleFun() {
      const { type } = this.detailInfo || {}
      if (type === "ECR") {
        this._startUser = Jw.getUser()
        this.variableList.forEach((item) => {
          if (item.name == "owner" || item.name == "cn_jwis_sharer") {
            item.users = [{ ...this._startUser }]
          }
        })
        findChangeInfoApi
          .execute({ oid: this.detailInfo.oid, list: [] })
          .then((res) => {
            this.findAutoRoleAct(res)
          })
      } else {
        this.findAutoRoleAct()
      }
    },
    //自动填充角色
    findAutoRoleAct(arr) {
      //自动填充人员
      const findAutoRole = ModelFactory.create({
        url: `${Jw.gateway}/${
          Jw.customerServer
        }/process/team/autoUser?workflowId=${this.selectWorkflow.key || ""}`,
        method: "post",
      })
      findAutoRole.execute(arr || this.selectedRows).then((resp) => {
        this.variableList.forEach((row) => {
          let role = resp.find((item) => item.name === row.name)
          if (role && _.isEmpty(row.users)) {
            row.users = role.users
          }
        })
      })
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      )
    },
    async getDetails() {
      let res = await fetchDetails.execute({ oid: this.detailInfo.oid })
      this.form.productContainer = res.containerOid
      this.form.modelDefinition = res.modelDefinition
      this.instanceData = res
      this.step1Params = res
      this.containerInfo = {
        oid: res.containerOid,
        type: res.containerType,
      }
      // this.$refs.addObjectContent.selectedRows = res.bizObjects;

      if (res.bizObjects) {
        this.selectedRows = [...res.bizObjects]
        this.defaultTableList = [...res.bizObjects]
      }

      this.selectWorkflow = this.workflowListData.find(
        (item) => item.id == res.processModelId
      )
      this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`
      this.variableList = res.teamRoleWithUsers
      this.onChangeRadio()
    },
    getContainerList() {
      fetchContainerList
        .execute({
          index: 1,
          size: 1000,
          searchKey: "",
        })
        .then((data) => {
          this.conOpts = data.rows
          if (this.conOpts.length > 0) {
            this.form.productContainer = this.conOpts[0].oid
          }
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onChangeConList(val) {
      this.containerInfo = this.conOpts.find((item) => item.oid === val) || {}
    },
    getSubModel() {
      this.modelDefinitionLoading = true
      fetchSubModel
        .execute({
          modelName: "ProcessOrder",
        })
        .then((res) => {
          this.typeOpts = res
          setTimeout(() => {
            if (this.typeOpts.length > 0) {
              this.form.modelDefinition = this.typeOpts[0].name
            }
          }, 0)
        })
        .catch((err) => {
          this.$error(err.msg)
        })
        .finally(() => {
          this.modelDefinitionLoading = false
        })
    },
    searchModelTable(params) {
      return fetchAllPart.execute(params)
    },
    async getDeployed() {
      console.log('获取允许发起的所有流程')
      let models = this.selectedRows.map((item) => {
        return {
          modelType: item.type,
          status: item.lifecycleStatus,
          btnType: item.btnType,
        }
      })
      let res = await fetchDeployed.execute({
        searchKey: this.searchKey,
        models,
      })
      //如果不是变更申请 过滤掉变更流程
      if (this.detailInfo.type != "ECR" && !_.isEmpty(res)) {
        res = res.filter((item) => !item.name.includes("变更"))
      }
      this.workflowList = res
      if (this.workflowListData.length > 0) {
        this.selectWorkflow = this.workflowListData[0]
        console.log(this.selectWorkflow)
        this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`
        this.onChangeRadio()
      } else {
        this.selectWorkflow = {
          name: "",
        }
      }
    },
    getTeam() {
      fetchTeam
        .execute({
          containerOid: getCookie("tenantOid"),
          containerType: "Tenant",
          keyword: "",
          pageNum: 1,
          pageSize: 1000,
        })
        .then((res) => {
          this.teamTempOpts = res
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    getVariables() {
      if (!this.selectWorkflow.deploymentId) {
        return
      }

      // 检查是否是 cn_jwis_partworkflow 且所有对象都是 Part 类型
      if (this.selectWorkflow.key === 'cn_jwis_partworkflow') {
        const objects = this.getObjects();
        console.log(objects);
        const allAreParts = objects && objects.every(obj => obj.masterType === 'Part');
        if (allAreParts) {
          // 如果都是 Part 类型，清空 variableList
          this.variableList = [];
          return;
        }
      }
      //  cn_yhht_wlstop
      if (this.selectWorkflow.key === 'cn_yhht_wlstop') {
        this.variableList = [];
        return;
      }
      if (this.selectWorkflow.key === 'cn_jwis_wjwf') {
        this.stepsInfo.additionaltranslation.outgoingType = "电子版本";
        this.stepsInfo.additionaltranslation.outgoingNumber = 1;
      }
      // 如果不是上述情况，则正常获取变量列表
      fetchVariables
        .execute({
          deploymentId: this.selectWorkflow.deploymentId,
        })
        .then((res) => {
          this.variableList = res.map((item) => {
            item.users = []
            return item
          })
          this.findAutoRoleFun()
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    getRoleWithUser() {
      if (!this.teamTemp) {
        this.variableList.forEach((item) => {
          item.users = []
        })
        return
      }
      fetchRoleWithUser
        .execute({
          teamTemplateOid: this.teamTemp,
        })
        .then((res) => {
          this.variableList.forEach((item) => {
            const info = res.find((val) => val.name == item.name)
            item.users = info ? info.users : []
          })
        })
        .catch((err) => {
          this.variableList = []
          this.$error(err.msg)
        })
    },
    onNextTo2() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let appBuilder = this.$refs.ref_appBuilder
          appBuilder &&
            appBuilder.validate().then(() => {
              let params = appBuilder.getValue()
              this.step1Params = params
              this.currentStep = 2
            })
        } else {
          return false
        }
      })
    },
    onNextTo3(val) {
      if (this.selectedRows.length > 0) {
        let underReviews = this.selectedRows.find(
          (item) => item.lifecycleStatus === "UnderReview"
        )
        if (underReviews) {
          this.$warning("存在审阅中的数据，请移除！")
          return
        }
        console.log('Selected Rows:', JSON.stringify(this.selectedRows, null, 2));

        // 前置校验逻辑
        const hasDraftStatus = this.selectedRows.some(item => item.lifecycleStatus === 'Draft');

        if (hasDraftStatus) {
          // 如果存在 '草稿' 状态的数据，开启校验
          const { isStatusConsisTency, invalidItem1 } = this.checkLifecycleStatusConsistency();
          if (!isStatusConsisTency) {
            this.$warning(`对象的状态不一致，请确认编码为 ${invalidItem1.number} 名称为 ${invalidItem1.name} 的对象`);
            return;
          }

          // const { isValid, invalidItem } = this.checkClsCode();

          //只有同一个分类的物料才允许一起提交
          // if (!isValid) {
          //   this.$warning(`对象的分类属性不一致，请确认编码为${invalidItem.number}名称为 ${invalidItem.name} 的对象`);
          //   return;
          // }

        }
        //校验所有RM03类型

       /* const requests = this.selectedRows.map(row => {
          if (row.clsDisplayName && row.clsDisplayName.startsWith("RM03")) {
            let params = {
              appliedType: 'JWIRawMaterial_Related_Object',
              mainObjectType: 'JWIRawMaterial'
            };
            return relationObjsModel.execute(params)
                .then(res => {
                  const relationRequests = res.map(relation => {
                    if (relation.relationDisplayName === "datasheet维护") {
                      const params = {
                        ...relation,
                        mainObjectOid: row.oid
                      };
                      return relationSearchModel.execute(params)
                          .then(data => {
                            if (data.length === 0) {
                              this.$warning("编码为" + row.number + "缺少datasheet维护信息");
                              return Promise.reject(new Error("Missing datasheet维护 information"));
                            }
                            console.log(data);
                            return data;
                          });
                    }
                  }).filter(request => request !== undefined);
                  return Promise.all(relationRequests);
                });
          }
        }).filter(request => request !== undefined);

        Promise.all(requests)
            .then(() => {
              this.currentStep = 3; // 所有校验通过，执行下一步操作
            })
            .catch(error => {
              // this.$error("处理请求时出现错误，请重试！");
            });*/

        this.currentStep = 3
      } else {
        if (val != 1) {
          this.$warning(this.$t("txt_please_seleted_data"))
        }
      }
    },
    onPrevious(val) {
      this.currentStep = val
      this.teamTemp = undefined
    },
    onSave(flag) {
      this.getParams()
      let api = createProcess
      if (this.detailInfo.oid && this.pageCode !== "objectProcess") {
        this.submitParams.oid = this.detailInfo.oid
        api = updateProcess
      } else {
        api = createProcess
      }
      this[flag + "Loading"] = true
      api
        .execute(this.submitParams)
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.$emit("getTableData")
          this.onCancel()
          this[flag + "Loading"] = false
          this.selectedRows = []
        })
        .catch((err) => {
          this[flag + "Loading"] = false
          this.$error(err.msg)
        })
    },
    onStart(flag) {
      //只有图纸及BOM发布流程才执行校验
      if (this.selectWorkflow.key === 'cn_jwis_partworkflow') {
        // this.checkIsReview();
      }
      // 校验 additionaltranslation
      this.validateObjectsAndCheckAdditionalTranslation(); // 校验 objects 和附加信息

      this.checkNodeAssignee()
      this.getParams()

      let api = createThenStartProcess
      if (this.detailInfo.oid && this.pageCode !== "objectProcess") {
        this.submitParams.oid = this.detailInfo.oid
        api = updateThenStartProcess
      } else {
        api = createThenStartProcess
      }
      if (this.getObjects()?.[0].type === "DocumentIteration") {
        api = createThenStartProcess
        this.submitParams.oid = null
      }
      this[flag + "Loading"] = true
      api
        .execute(this.submitParams)
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.$emit("getTableData")
          this.onCancel()
          this[flag + "Loading"] = false
          this.selectedRows = []
          this.clearReviewAndOutgoing(); // 清除状态值
        })
        .catch((err) => {
          this[flag + "Loading"] = false
          this.$error(err.msg)
        })
    },
    beforeUpload(file) {
      if (!this.fileCheck(file)) return false;
      this._uploadFile = { name: file.name, status: "uploading" };
      this.fileList.push(this._uploadFile);

      this.retrieveNewURL(file, (url) => {
        // 上传文件到服务器
        this.uploadFile(url, file);
      });
      return false;
    },
    validateObjectsAndCheckAdditionalTranslation() {
      // 获取对象数组
      const objects = this.getObjects();
      let key = this.selectWorkflow.key
      // 校验是否有 A01 版本且 masterType 是 Part 的物料,并且 是BOM、图纸发布流程
      const hasInvalidA01Part = objects.some(obj => {
        return obj.masterType === "Part" && obj.version === "A01" && obj.lifecycleStatus==='Draft' && 'cn_jwis_partworkflow' === key ;
      });

      // 如果存在无效的 A01 物料，直接提示错误
      if (hasInvalidA01Part) {
        const errorMsg = "A01草稿版本的物料不允许发起流程，请检查！";
        this.$warning(errorMsg); // 提示错误
        throw new Error(errorMsg); // 终止后续流程
      }

      // 遍历校验 masterType 和 version
      const needsAdditionalTranslationCheck = objects.some(obj => {
        // 校验条件：masterType 不是 "Part" 或 version 不是 "A01"
        // return obj.masterType !== "Part" && obj.version !== "A01";
        return obj.displayVersion && !obj.displayVersion.startsWith("A01");
      });

      // 如果需要触发校验，调用 checkAdditionalTranslation
      if (needsAdditionalTranslationCheck && key === "cn_jwis_partworkflow") {
        if (!this.shouldShowDivAsyncResult) {
          // 异步校验未通过，取消附加信息校验
          return;
        }
        try {
          this.checkAdditionalTranslation(); // 执行附加信息的校验
        } catch (error) {
          console.error("附加信息校验失败:", error.message);
          throw error; // 终止后续流程
        }
      }
    },
    checkAdditionalTranslation() {
      const additionalData = this.stepsInfo.additionaltranslation;

      // 定义需要校验的字段和对应的提示信息
      const requiredFields = [
        { field: 'changeType', message: '请选择更改类型' },
        { field: 'changeReason', message: '请输入更改原因' },
        { field: "changePlan", message: "请输入更改方案" },
        { field: "affectedObjects", message: "请输入更改受影响对象" },
        { field: "impactAnalysis", message: "请输入更改影响分析" }
      ];

      // 遍历字段进行校验
      for (const { field, message } of requiredFields) {
        if (!additionalData[field] || additionalData[field].toString().trim() === "") {
          this.$warning(message);
          throw new Error(message); // 抛出错误以中断流程
        }
      }

      // 单独校验 changeAttachments--11.26暂时去掉校验
     /* if (
          !additionalData.changeAttachments || // 确保 changeAttachments 存在
          !Array.isArray(additionalData.changeAttachments) || // 如果不是数组，检查是否为嵌套结构
          (Array.isArray(additionalData.changeAttachments) && additionalData.changeAttachments.length === 0)
      ) {
        const message = "请上传至少一个附件";
        this.$warning(message);
        throw new Error(message);
      }*/
    },
    checkNodeAssignee() {
      let key = this.selectWorkflow.key
      let checkKeys = [] //会签节点要检查的流程key
      let teamContent = []
      for (let i = 0; i < this.variableList.length; i++) {
        const item = this.variableList[i]
        let isCheck = !item.countersignFlag || checkKeys.includes(key)
        if (!item.users.length && isCheck) {
          this.$warning("流程角色请选择用户")
          throw new Error("流程角色请选择用户")
        }
      }
    },
    getParams() {
      let {  additionaltranslation } = this.stepsInfo;
      let teamContent = this.variableList.map((item) => {
        return {
          roleName: item.name,
          name: item.displayName,
          users: item.users.map((val) => val.account),
          userAccounts: item.users.map((val) => val.account),
        }
      })
      let id = "",
        type = ""
      if (this.pageCode === "processManage") {
        id = this.$route.query.oid
        type = this.$route.query.type
      } else if (this.pageCode === "objectProcess") {
        id = this.detailInfo.containerOid
        type = this.detailInfo.containerType
      } else if (this.pageCode === "myProcess") {
        id = this.containerInfo.oid
        type = this.containerInfo.type
      } else if (this.pageCode === "processRecord") {
        id = this.objectDetailsData.containerOid
        type = this.objectDetailsData.containerType
      }
      this.submitParams = {
        locationInfo: {
          catalogOid: id,
          catalogType: type,
          containerOid: id,
          containerType: type,
        },
        modelDefinition: this.form.modelDefinition,
        bizObjects: this.getObjects(),
        processModelId: this.selectWorkflow.id,
        teamContent: teamContent,
        name: this.selectWorkflow.name,
        extensionContent: {
          docInfo: {},
          additionalTranslation: { ...this.stepsInfo.additionaltranslation }, // 添加附加信息
        },
        ...additionaltranslation
      }
    },

    getObjects() {
      if (
        this.pageCode === "objectProcess" &&
        this.detailInfo &&
        this.detailInfo.oid
      ) {
        return [this.detailInfo]
      } else if (this.selectedRows && this.selectedRows.length !== 0) {
        return this.selectedRows.map((item) => {
          item.primary = item.selectParendOid ? false : true
          return item
        })
      } else if (this.step1Params && this.step1Params.oid) {
        return this.step1Params.bizObjects
      }
    },

    onSearch() {
      this.getDeployed()
    },
    onChangeRadio() {
      this.teamTemp = undefined
      this.variableList = []
      this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`
      this.getVariables()
    },
    onAddUser(item) {
      this.$refs["user-modal"]
        .show({
          type: "User",
        })
        .then((data) => {
          let oids = item.users.map((item) => item.oid)
          data.forEach((val) => {
            if (!oids.includes(val.oid)) {
              item.users.push(val)
            }
          })
        })
    },
    onDeleteUser(list, item) {
      let index = list.indexOf(item)
      list.splice(index, 1)
    },
    onCancel() {
      this.currentStep = 0
      this.step1Params = {}
      this.instanceData = {}
      this.$refs.addForm && this.$refs.addForm.resetFields()
      this.$refs.addForm && this.$refs.addForm.clearValidate()
      this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate()
      this.teamTemp = undefined
      this.variableList = []
      this.submitParams = {}
      this.$emit("close")
    },
    findWorkflow(type) {
      let params = {
        containerOid: getCookie("tenantOid"), // this.detailInfo.containerOid
        modelCode: type,
        modelOid: this.detailInfo.oid,
      }
      findWorkflow
        .execute(params)
        .then((res) => {
          if (res) {
            this.onlyWorkflowModelId = res.workflowModelId
          }
        })
        .catch((err) => {
          this.$error(err.msg)
        })
        .finally(() => {
          if (this.typeList.size !== 1) {
            this.onNextTo3(1)
          }
        })
    },
    checkClsCode() {
      //校验是否同一分类 clsCode+clsDisplayName 或 用number的小分类码
      if (!this.selectedRows.length) return { isValid: false, invalidItem: null };

      const firstClsCode = this.selectedRows[0].clsCode;
      const firstClsDisplayName = this.selectedRows[0].clsDisplayName;
      const firstNumber = this.selectedRows[0].number ? this.selectedRows[0].number.split('-')[0] : null;

      const invalidItem = this.selectedRows.find(row => {
        // 如果不是 PartIteration，直接通过
        // if (row.type !== 'PartIteration') return false;
        const currentNumber = row.number ? row.number.split('-')[0] : null;
        return (row.clsCode !== firstClsCode || row.clsDisplayName !== firstClsDisplayName) && currentNumber !== firstNumber;
      });

      return { isValid: !invalidItem, invalidItem };
    },
    checkLifecycleStatusConsistency() {
      // 如果对象数组为空，直接返回校验不通过
      if (!this.selectedRows.length) return { isStatusConsisTency: false, invalidItem1: null };

      // 获取第一个对象的lifecycleStatus属性值
      const firstLifecycleStatus = this.selectedRows[0].lifecycleStatus;

      // 查找第一个不一致的对象
      const invalidItem1 = this.selectedRows.find(obj => obj.lifecycleStatus !== firstLifecycleStatus);

      // 返回校验结果
      return { isStatusConsisTency: !invalidItem1, invalidItem1 };
    },
    fileCheck(file) {
      let isoutRange = this.fileList.length == this._maxMultiple;

      if (isoutRange) {
        this.$error(this.$t('txt_up_upload')+`${this._maxMultiple}`+this.$t('txt_a_feil'));
        return false;
      }

      let type = file.name;
      type = type.substr(type.lastIndexOf(".") + 1).toLowerCase();

      if (this._accept) {
        let isoutFormat = !this._accept.includes(type);
        if (isoutFormat) {
          this.$error(`${this.$t('txt_upload_feil_fromat')}${this._accept}`);
          return false;
        }
      } else {
        return true;
      }

      let isoutSize = file.size / 1024 / 1024 >= this._maxSize;
      if (isoutSize) {
        this.$error(this.$t('txt_feil_size_20'));
        return false;
      }
      return true;
    },
    retrieveNewURL(file, cb) {
      let timestamp = new Date().getTime();
      this._fileParams.fileSize = file.size;
      this._fileParams.fileName = timestamp + "_" + file.name;
      let param = {
        fileName: this._fileParams.fileName,
      };
      this.loading = true;
      preSignedPutUrl
          .execute(param)
          .then((res) => {
            let url = decodeURIComponent(res.url);
            this._fileParams.bucketName = res.bucketName;

            cb(url);
          })
          .catch((err) => {
            this._uploadFile.status = "error";
          });
    },
    uploadFile(url, file) {
      axios
          .put(url, file, {
            onUploadProgress: (progressEvent) => {
              file.status = "uploading";
              this.percent = file.percentage = parseInt(
                  (progressEvent.loaded / progressEvent.total) * 100
              );

              if (file.percentage == 100) {
                this.percent = 0;
                file.status = "success";
              }
            },
          })
          .then((res) => {
            this.createFileMetadata(file);
          })
          .catch((err) => {
            this._uploadFile.status = "error";
          });
    },
    createFileMetadata(file) {
      FileMetadataApi.execute(this._fileParams)
          .then((res) => {
            res.name = res.fileOriginalName;
            res.description = res.oid;
            Object.assign(this._uploadFile, res, { status: "done" });

            // 更新为直接的数组结构
            this.stepsInfo.additionaltranslation.changeAttachments = this.fileList.map((item) => ({
              name: item.fileOriginalName, // 文件名
              oid: item.oid,              // 文件唯一标识符
              url: item.filePath,         // 文件下载路径
            }));
            this.$emit("change", this.fileList);
          })
          .catch((err) => {
            this._uploadFile.status = "error";
          });
    },
    // 独立的清除方法
    clearReviewAndOutgoing() {
      this.stepsInfo.additionaltranslation.isOutgoing = null;
    },
    checkIsReview() {
      // 检查"是否外发"是否已填写

      const {isOutgoing} = this.stepsInfo.additionaltranslation;
      if (!isOutgoing) {
        this.$error('请填写是否外发');
        throw new Error("Missing field: isOutgoing");  // 阻止流程继续执行
      }
    },
    handleDivCheck() {
      // 只在shouldShowDiv为true时才进行异步校验
      if (this.shouldShowDiv) {
        this.asyncCheckDiv();
      } else {
        this.shouldShowDivAsyncResult = false;
      }
    },
    async asyncCheckDiv() {
      try {
        const objects = this.getObjects();
        if (!objects || !Array.isArray(objects) || objects.length === 0) {
          this.shouldShowDivAsyncResult = false;
          return;
        }
        // 传递对象结构，避免直接传数组
        const res = await verifyContent.execute({ bizObjects: objects });
        this.shouldShowDivAsyncResult = res && (res.result === undefined || res.result === true);
        console.log(this.shouldShowDivAsyncResult);
      } catch (e) {
        this.shouldShowDivAsyncResult = false;
      }
    },
  },
}
</script>

<style lang="less" scoped>
.steps-wrap {
  display: flex;
  .step-item {
    display: flex;
    justify-content: center;
    width: 50%;
    padding: 4px 0;
    background: rgba(30, 32, 42, 0.04);
    .step-num {
      width: 24px;
      height: 24px;
      line-height: 24px;
      margin-right: 8px;
      text-align: center;
      color: #9ca1ad;
      background: #c6cbd7;
      border-radius: 50%;
    }
    .step-title {
      line-height: 24px;
    }
    &.is-current {
      background: #a4c9fc;
      .step-num {
        color: #fff;
        background: #265dd8;
      }
      .step-title {
        color: #505d77;
      }
    }
  }
}
.step-body {
  height: 500px;
  margin-top: 20px;
  overflow-x: hidden;
  overflow-y: auto;
}
.step-body3 {
  display: flex;
  .step3-left {
    width: 30%;
    padding-right: 2px;
    .search-input {
      width: 60%;
    }
    .radio-wrap {
      height: 400px;
      margin-top: 8px;
      overflow: auto;
    }
    .ant-radio-group {
      width: 100%;
    }
    .ant-radio-wrapper {
      display: block;
      line-height: 32px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .step3-right {
    width: 70%;
    padding: 0 2px 0 16px;
    color: rgba(30, 32, 42, 0.85);
    border-left: 1px solid rgba(30, 32, 42, 0.15);
    overflow: auto;
    .right-title {
      font-size: 16px;
      color: rgba(30, 32, 42, 0.85);
      font-weight: 700;
    }
    .right-tip {
      margin: 5px 0;
      font-size: 12px;
      color: rgba(30, 32, 42, 0.45);
    }
    .right-process-img {
      width: 100%;
      height: 300px;
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .right-temp-wrap {
      margin: 15px 0;
      .ant-select {
        width: 60%;
        margin-top: 8px;
      }
    }
    .right-handler-wrap {
      .handler-item {
        margin-bottom: 16px;
        .item-head {
          margin-bottom: 8px;
          i {
            margin-left: 8px;
          }
        }
        .item-body {
          display: flex;
        }
        .handler-add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          min-width: 32px;
          height: 32px;
          margin-right: 8px;
          background: #f0f7ff;
          border: 1px solid #a4c9fc;
          border-radius: 50%;
          cursor: pointer;
        }
        .handlers {
          display: flex;
          flex-wrap: wrap;
        }
        .handler {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 8px;
          margin: 0 8px 8px 0;
          background: rgba(30, 32, 42, 0.04);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          .close-icon {
            position: absolute;
            top: -13px;
            right: -8px;
            visibility: hidden;
          }
          .avatar {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
            .ant-avatar {
              width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }
          &:hover {
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
            .close-icon {
              visibility: visible;
            }
          }
        }
      }
    }
  }
}
.foot-btns {
  margin: 15px 0 0;
  text-align: right;
  .form-foot-btn {
    margin-left: 5px;
  }
}

// 统一表单项样式
.form-item {
  width: 280px !important;

  // 针对 a-input-number 的特殊处理
  &.ant-input-number {
    width: 280px !important;
  }
}

// 确保所有表单控件都有统一的宽度
.step3-right {
  .ant-form-model-item {
    .ant-select,
    .ant-input,
    .ant-input-number {
      width: 280px !important;
    }
  }
}
</style>
