<template>
  <div>
    <a-modal
        destroy-on-close
      v-model.trim="visibleshow"
      :title="title"
      width="80%"
      :okText="okText"
      :cancelText="$t('btn_cancel')"
      @ok="confirmbtn"
      :confirmLoading="btnloading"
    >
      <select-list-table
        ref="select-list-table"
        :selectList="selectList"
        :modalSelectedRows.sync="modalSelectedRows"
        :type="type"
        :splitcode="splitcode"
        :beforcode="beforcode"
        @addrelationrow="addrelationrow"
      />
    </a-modal>

    <!-- 选择关联对象 -->
    <select-relation-dialog
      :visible.sync="relationshow"
      :rowData="relationRowData"
      :defaultSelect="defaultRelationSelect"
      @changeselect="changerelationselect"
      @removeSelect="removeSelect"
    />
    <base-color-modal
      :title="operationTitle"
      :width="992"
      :visible.sync="operationVisible"
      dialog-class="operation-modal"
      :ok-text="
        $t('btn_ok') +
        `${
          operationTitle == $t('txt_set_status')
            ? $t('btn_set_s')
            : operationTitle
        }`
      "
      @ok="modalOk"
      :okBtnLoading="okBtnLoading"
      @cancel="operationCancel"
      :body-style="{ height: '545px', overflowY: 'scroll' }"
    >
      <!-- 设置状态 -->
      <div
        class="setStatus-container"
        :style="{
          width:
            statusList.length * 120 > 992 - 24 * 2
              ? statusList.length * 120 + 'px'
              : '100%',
        }"
      >
        <div
          v-for="(item, index) in statusList"
          :key="item.code"
          :class="[
            'item-box',
            {
              current: item.code == lifecycleStatus,
              active: newStatus == item.code,
            },
          ]"
          @click="setStatusItem(item)"
        >
          <div class="circle">
            <div v-if="index != 0" class="line left-arrow"></div>
            <div
              v-if="index != statusList.length - 1"
              class="line right-arrow"
            ></div>
          </div>
          <div class="status">
            <a-tag style="margin: 0 auto">{{ item.displayName }}</a-tag>
          </div>
          <!-- 当前状态 -->
          <div v-if="item.code == lifecycleStatus" class="text">
            <span
              class="jwi-iconflag"
              style="color: #f6445a; margin-right: 4px"
            ></span>
            {{ $t("txt_current_state") }}
          </div>
          <div v-else class="text">{{ $t("txt_set_current") }}</div>
        </div>
      </div>
    </base-color-modal>
    <!-- 批量移动复用 -->
    <create-part-dialog
      ref="create-part-dialog"
      :batchOperatorType="type"
      :defaultposition="{ oid: locationDefaultOid }"
      @batchOperator="batchOperator"
    />

    <!-- 发起流程 -->
    <!-- <start-process-modal
      :visible.sync="processVisible"
      :pageCode="'objectProcess'"
      :detailInfo="{}"
      :batchProcess="true"
      @submit="startProcess"
      @close="processVisible = false"
    ></start-process-modal> -->

    <!-- 添加至基线 -->
    <SelectBaseLine
      v-if="visibleshow"
      :selectList="selectList"
      @batchBaseLine="batchBaseLine"
      :visible.sync="baseLineVisible"
    />
  </div>
</template>

<script>
import StartProcessModal from "/views/product-content/process-manage/start-process-modal"
import SelectRelationDialog from "./select-relation-dialog.vue"
import CreatePartDialog from "../../../components/dropdowncomponents/create-part-dialog.vue"
import SelectBaseLine from "./select-base-line.vue"
import SelectListTable from "./select-list-table.vue"
import { findConf, updateStage } from "../api/index"
import ModelFactory from "jw_apis/model-factory"
import baseColorModal from "components/base-color-modal.vue";
import  {
    searchStatusList
} from 'apis/part'
import { getCookie } from "jw_utils/cookie"

const partBatchDeleteByMasterOid = ModelFactory.create({
  url: `${Jw.gateway}/part-bom-micro/part/batchDeleteByMasterOid`,
  method: "post",
})
const mcadBatchDeleteByMasterOid = ModelFactory.create({
  url: `${Jw.gateway}/part-bom-micro/part/batchDeleteByMasterOid`,
  method: "post",
})
const ecadBatchDeleteByMasterOid = ModelFactory.create({
  url: `${Jw.gateway}/part-bom-micro/part/batchDeleteByMasterOid`,
  method: "post",
})
const batchSendHZZ = ModelFactory.create({
  url: `${Jw.gateway}/customer/integration/batchSendHZZ`,
  method: "post",
})
const batchSendERP = ModelFactory.create({
  url: `${Jw.gateway}/customer/integration/batchSendERP`,
  method: "post",
})

const idsStatusApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/documentMilkyWay/sendIDS`,
  method: 'post'
})

const batchSetStatus = ModelFactory.create({
  url: `${Jw.gateway}/customer/common/batchSetStatus`,
  method: "post",
})
export default {
  components: {
    SelectRelationDialog,
    CreatePartDialog,
    StartProcessModal,
    SelectBaseLine,
    SelectListTable,
    baseColorModal
  },
  props: {
    type: {
      type: String,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: this.$t("txt_batch_operation"),
      okText: this.$t("btn_ok"),
      //收集对象弹窗
      relationshow: false,
      relationRowData: {},

      defaultRelationSelect: [],

      btnloading: false,

      processVisible: false,

      baseLineVisible: false,

      // table选中项
      modalSelectedRows: [],

      splitcode: "_",
      beforcode: "",

      locationDefaultOid: "",
      operationVisible:false,
      operationTitle:this.$t("txt_set_status"),
      okBtnLoading:false,
      newStatus:'',
      lifecycleStatus:'',
      statusList:[]
    }
  },
  created() {
    localStorage.removeItem("contenttreelocation")
    this.loadconfig()
  },
  watch: {
    visibleshow: function (val) {
      if (this.$refs["select-list-table"]) {
        this.$refs["select-list-table"].inittabledata(val)
      }
      if (val) {
        this.init()
        this.clearnewname()
        this.loadconfig()
      }
    },
    modalSelectedRows: function (val) {
      console.log("改变选择项", val)
    },
  },
  computed: {
    visibleshow: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit("update:visible", val)
      },
    },
  },
  methods: {
    //清理已改名称
    clearnewname() {
      this.selectList.forEach((item) => {
        delete item.newname
      })
      if (this.$refs["select-list-table"]) {
        this.$refs["select-list-table"].rownewnames = []
      }
    },
    //加载首选项配置
    loadconfig() {
      // this.splitcode = '_'
      if (this.type === 'saveAs') {
        this.splitcode = "";
        return;
      }

      if (this.type === 'forming'){
        this.splitcode = "(未成型)"
      }else {
        findConf("Naming_Rules").then((resp) => {
          let befor = resp.find((item) => item.name === "saveas_befor")
          if (befor) {
            this.beforcode = befor.value
          }
          let after = resp.find((item) => item.name === "saveas_after")
          if (after) {
            this.splitcode = after.value
          }
        })
      }
    },
    addrelationrow(row, datalist) {
      this.relationRowData = Object.assign({}, row)
      this.initRelationSelect(datalist)
      this.relationshow = true
    },
    //初始化默认选中项
    initRelationSelect(datalist) {
      let selectDataList = datalist.filter(
        (item) => item.selectParendOid === this.relationRowData.oid
      )
      if (selectDataList) {
        this.defaultRelationSelect = selectDataList.map((item) => {
          let exam = Object.assign({}, item)
          delete exam.selectParendOid
          return exam
        })
      }
    },
    startProcess(val) {
      this.$emit("batchProcess", this.modalSelectedRows, val)
    },

    batchBaseLine(oid) {
      this.$emit("batchBaseLine", oid, this.modalSelectedRows)
    },

    onCloseProcessModal() {
      console.log("关闭发起流程")
    },
    //已添加select
    changerelationselect(val, selectParendOid) {
      this.$refs["select-list-table"].changerelationselect(val, selectParendOid)
    },
    //支持收集相关对象删除功能
    removeSelect(rows) {
      if (this.$refs["select-list-table"]) {
        rows.forEach((item) => {
          this.$refs["select-list-table"].removerow(item)
        })
      }
    },
    //修改阶段
    updateStageFun(list, currentStage) {
      updateStage(list, currentStage)
        .then((resp) => {
          this.$success(this.$t("msg_update_success"))
        })
        .catch((e) => {
          this.$error(e)
        })
    },
    //点击确认选择行
    confirmbtn() {
      if (this.modalSelectedRows.length == 0) {
        this.$warning(this.$t("txt_pls_data"))
        return
      }

      this.locationDefaultOid = localStorage.getItem("contenttreelocation")

      let rownewnames = this.$refs["select-list-table"].rownewnames
      this.modalSelectedRows.forEach((item) => {
        if (rownewnames[item.oid]) {
          item.newname = rownewnames[item.oid]
        }
      })

      if (
        this.type === "changestage" &&
        !this.$refs["select-list-table"].currentStage
      ) {
        this.$warning(this.$t("change_stage_input"))
        return
      }

      switch (this.type) {
        case "move": {
          this.$refs["create-part-dialog"].init({})
          break
        }
        case "changestage":
        case "saveAs": {
          this.$refs["create-part-dialog"].init2({}, this.modalSelectedRows)
          // modalSelectedRows
          break
        }
        case "screen": {
          let treeData =
              {
                containerOid: this.modalSelectedRows[0].containerOid,
                containerType: this.modalSelectedRows[0].containerType,
                oid: this.selectList[0].location.catalogOid,
                type: this.selectList[0].location.catalogType
              };
          this.batchOperator(treeData);
          // this.$refs["create-part-dialog"].init2({}, this.modalSelectedRows)
          // modalSelectedRows
          break
        }
        case "forming": {
          let treeData =
              {
                containerOid: this.modalSelectedRows[0].containerOid,
                containerType: this.modalSelectedRows[0].containerType,
                oid: this.selectList[0].location.catalogOid,
                type: this.selectList[0].location.catalogType
              };
          // this.batchOperator(this.selectList);
          this.batchOperator(treeData);
          // this.$refs["create-part-dialog"].init2({}, this.modalSelectedRows)
          // modalSelectedRows
          break
        }
        case "sendErp": {
          this.sendErp();
          // modalSelectedRows
          break
        }
        case "sendHZZ": {
          this.sendHZZ();
          // modalSelectedRows
          break
        }
        case "setStatus": {
          this.setStatus();
          // modalSelectedRows
          break
        }
        case "process": {
          //批量发起流程
          this.processVisible = true
          break
        }
        case "addBaseLine": {
          this.baseLineVisible = true
          break
        }
        case "idsStatus": {
          this.idsStatusChange()
          break;
        }
        case "exportBOM": {
          this.exportBOM()
          break;
        }
        case "batchDeleteByMasterOid": {
          //批量删除迭代
          this.batchDeleteByMasterOid();
          break;
        }
        default:
          break
      }
    },
    //修改ids状态
    idsStatusChange(){
      let list = this.modalSelectedRows
      let data = list.map(ele=>{
        return ele.oid
      })
      this.btnloading=true
      idsStatusApi.execute(data).then((res) => {
        this.$success(this.$t("msg_success"));
        this.btnloading=false
        this.visibleshow = false
      })
      .catch((e) => {
        this.btnloading=false
        this.$error(e.msg);
      })
    },
    //批量发送ERP
    sendErp(){
      let list = this.modalSelectedRows
      let data=list.map(ele=>{
        return {
          type:ele.type,
          oid:ele.oid,
          masterOid:ele.masterOid,
          masterType:ele.masterType,
          number:ele.number
        }
      })
      this.btnloading=true
      batchSendERP.execute(data).then((res) => {
        this.$success(this.$t("msg_success"));
        this.selectList=[]
        this.btnloading=false
        this.visibleshow = false
      })
      .catch((e) => {
        this.btnloading=false
        this.$error(e.msg);
      })
    },

    //批量发送HZZ
    sendHZZ(){
      let list = this.modalSelectedRows
      let data=list.map(ele=>{
        return {
          type:ele.type,
          oid:ele.oid,
          masterOid:ele.masterOid,
          masterType:ele.masterType,
          number:ele.number
        }
      })
      this.btnloading=true
      batchSendHZZ.execute(data).then((res) => {
        this.$success(this.$t("msg_success"));
        this.selectList=[]
        this.btnloading=false
        this.visibleshow = false
      })
          .catch((e) => {
            this.btnloading=false
            this.$error(e.msg);
          })
    },
    //批量删除迭代
    batchDeleteByMasterOid() {
      // 从每个选中的行对象中，只提取出 masterOid 字符串，组成一个新的数组
      const masterOids = this.modalSelectedRows.map(ele => ele.masterOid);
      // 校验选中项的lifecycleStatus
      let hasError = false;
      for (let row of this.modalSelectedRows) {
        if (row.lifecycleStatus === 'Released' || row.lifecycleStatus === 'UnderReview') {
          hasError = true;
          break;
        }
      }
      if (hasError) {
        this.$error("选中的对象状态为 已发布 或 审阅中，不允许删除");
        return;
      }

      // 如果没有选择任何数据，则给出提示并停止执行
      if (!masterOids || masterOids.length === 0) {
        this.$warning("请先选择要删除的数据");
        return;
      }

      // 获取选中的数据信息用于确认提示
      const selectedCount = this.modalSelectedRows.length;
      const selectedNumbers = this.modalSelectedRows.map(item => item.number || item.name).slice(0, 3);
      const displayText = selectedNumbers.join('、') + (selectedCount > 3 ? `等${selectedCount}个迭代` : '');

      // 二次确认删除
      this.$confirm({
        width: "400px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            批量删除
          </p>
        ),
        content: (
          <div>
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);margin-bottom:8px;">
              您确定要删除以下数据吗？
            </p>
            <p style="font-size:14px;font-weight:500;color:rgba(30, 32, 42, 0.85);margin-bottom:8px;">
              {displayText}
            </p>
            <p style="font-size:14px;font-weight:400;color:#ff4d4f;">
              删除后无法恢复，请谨慎操作！
            </p>
          </div>
        ),
        cancelText: "取消",
        okText: "确定删除",
        onOk: () => {
          this.executeDelete(masterOids);
        },
      });
    },

    // 执行删除操作
    executeDelete(masterOids) {
      this.btnloading = true;
      // 将提取出的 masterOids 数组作为参数传递给接口
      partBatchDeleteByMasterOid.execute(masterOids).then(() => {
        this.$success("删除成功");
        this.btnloading = false;
        this.visibleshow = false;
        // 通过事件通知父组件清除选中数据
        // 触发列表刷新
        this.$emit('reloadpage');
      })
          .catch((e) => {
            this.btnloading = false;
            this.$error(e.msg || '删除失败，请稍后重试');
          });
    },
    //导出BOM
    exportBOM() {
      // 获取选中行的oid列表
      let oidList = this.modalSelectedRows.map(item => item.oid);

      // 构建请求参数
      let param = {
        oidList: oidList
      };

      // 获取token
      const accesstoken = getCookie("token");

      // 设置loading状态
      this.btnloading = true;

      // 发起请求
      fetch(
          `${Jw.gateway}/customer/customerContainer/exportBOMExcel`,
          {
            method: "post",
            body: JSON.stringify(param),
            headers: {
              "Content-Type": "application/json;charset=utf8",
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie("tenantAlias"),
              tenantOid: getCookie("tenantOid")
            }
          }
      )
          .then(response => {
            return response.blob();
          })
          .then(data => {
            this.$success(this.$t("txt_export_success"));
            let timestamp = new Date().getTime();
            const fileName = `批量BOM导出_${timestamp}`;
            this.downBlob(data, fileName);
            this.btnloading = false;
            this.visibleshow = false;
          })
          .catch(err => {
            console.error(err);
            this.btnloading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    // 添加下载文件的方法
    downBlob(blob, fileName) {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = `${fileName}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(link.href);
    },
    downBlobZIP(data,name,fileType = 'zip'){
      let url = window.URL.createObjectURL(
          new Blob([data], {
            type: "application/zip;application/zip",
          })
      )
      let link = document.createElement("a")
      link.href = url
      link.setAttribute("download", `${name}.${fileType}`)
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(url)
    },

    setStatusItem(item){
      this.newStatus=item.code
    },
    setStatus(){
      let lifecycleOid=this.modalSelectedRows[0].lifecycleOid
      this.lifecycleStatus=this.modalSelectedRows[0].lifecycleStatus
      searchStatusList
        .execute({
          oid: lifecycleOid,
        })
        .then((res) => {
          this.statusList = (res || { context: {} }).context.states || [];
          this.operationVisible = true;
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    modalOk() {
      let list = this.modalSelectedRows
      let data=list.map(ele=>{
        return {
          modelInfo:{
            modelDefinition:ele.modelDefinition,
            oid:ele.oid,
            type:ele.type
          },
          status:this.newStatus||this.lifecycleStatus
        }
      })
      this.okBtnLoading=true
      batchSetStatus.execute(data).then((res) => {
        this.$success(this.$t("msg_success"));
        this.$emit('reloadpage')
        this.selectList=[]
        this.okBtnLoading=false
        this.visibleshow = false
        this.operationVisible = false;
      })
      .catch((e) => {
        this.okBtnLoading=false
        this.$error(e.msg);
      })
    },
    operationCancel() {
      this.lifecycleStatus=''
      this.newStatus=''
      this.operationVisible = false;
    },
    batchOperator(treeSelect) {
      this.$emit(
        "batchOperator",
        this.modalSelectedRows,
        treeSelect,
        this.splitcode,
        this.beforcode,
        this.$refs["select-list-table"].currentStage
      )
    },
    init() {
      switch (this.type) {
        case "move":
          //移动
          this.title = this.$t("txt_move_obj")
          this.okText = this.$t("txt_select_location")
          break

        case "saveAs": {
          this.title = this.$t("txt_saveas_obj")
          this.okText = this.$t("txt_select_location")
          break
        }
        case "screen": {
          this.title = this.$t("txt_screen_obj")
          this.okText = '确定'
          break
        }
        case "forming": {
          this.title = this.$t("txt_forming_obj")
          this.okText = '确定'
          break
        }
        case "process": {
          this.title = this.$t("txt_initiate_process")
          this.okText = this.$t("txt_baseline_model")
          break
        }
        case "addBaseLine": {
          this.title = this.$t("txt_add_to_baseline")
          this.okText = this.$t("txt_seletct_baseline")
          break
        }
        case "changestage": {
          this.title = this.$t("change_stage")
          break
        }
        case "sendErp": {
          this.title = '发送ERP'
          this.okText = '确认'
          break
        }
        case "sendHZZ": {
          this.title = '发送慧致造'
          this.okText = '确认'
          break
        }
        case "setStatus": {
          this.title = '设置状态'
          this.okText = '设置状态'
          break
        }
        case "idsStatus": {
          this.title = "撤销IDS系统状态"
          this.okText = '确认'
          break
        }
        case "exportBOM": {
          this.title = "批量导出BOM"
          this.okText = '确认'
          break
        }
        default:
          break
      }
    },
    fetchModalTable() {
      console.log("加载table")
    },
  },
}
</script>

<style lang="less" scoped>
  .setStatus-container {
    display: flex;
    justify-content: center;
    margin-top: 300px;

    .item-box {
      //   width: 120px;
      height: 142px;
      padding: 20px 12px 8px;
      border-radius: 5px;
      margin-right: 12px;
      cursor: pointer;
      transition: all 0.3s;
      flex-basis: 120px;
      border: 1px solid transparent;

      .circle {
        width: 16px;
        height: 16px;
        margin: 0 auto;
        border: 12px solid #a4c9fc;
        border-radius: 50%;
        box-sizing: content-box;
        position: relative;

        .line {
          position: absolute;
          top: 6px;
        }

        .left-arrow,
        .right-arrow {
          height: 1px;
          background: rgba(30, 32, 42, 0.15);
        }
        .left-arrow {
          width: 38px;
          left: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-left: 8px solid rgba(30, 32, 42, 0.15);
            border-bottom: 4px solid transparent;
          }
        }
        .right-arrow {
          width: 42px;
          right: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            left: -8px;
            top: -4px;
            width: 8px;
            height: 8px;
            background: rgba(30, 32, 42, 0.15);
            border-radius: 50%;
          }
        }
      }
      .status {
        text-align: center;
        margin-top: 8px;
      }
      .text {
        text-align: center;
        opacity: 0;
        transition: all 0.3s;
        font-size: 12px;
        white-space: nowrap;
      }

      &:not(.current) {
        .text {
          margin-top: 10px;
          height: 32px;
          line-height: 32px;
          background: #ffffff;
          border: 1px solid rgba(30, 32, 42, 0.15);
          border-radius: 4px;
        }
      }
      &:hover {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
      &.current {
        background: #f0f7ff;
        border: 1px solid #a4c9fc;

        .text {
          opacity: 1;
          margin-top: 15px;
          height: 22px;
        }
      }
      &.active {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
    }
  }
</style>
