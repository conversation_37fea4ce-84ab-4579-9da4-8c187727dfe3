<template>
  <div>
    <a-dropdown>
      <a-button>
        {{ $t("btn_batch_operation") }}
        <jw-icon type="jwi-arrow-down" />
      </a-button>

      <a-menu slot="overlay">
        <a-spin v-if="loading" style="margin: 20px 65px" />
        <a-menu-item v-if="!loading" @click="validSelect('move')">
          <span class="jwi-iconmove" style="margin-right: 8px"></span>
          {{ $t("txt_mobile") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('changestage')">
          <span class="jwi-tihuan" style="margin-right: 8px"></span>
          {{ $t("change_stage") }}
        </a-menu-item>

        <a-menu-item v-if="!loading" @click="validSelect('saveAs')">
          <span class="jwi-iconsave-as" style="margin-right: 8px"></span>
          {{ $t("txt_save") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('process')">
          <span
            class="jwi-iconSelect-processing-1"
            style="margin-right: 8px"
          ></span>
          {{ $t("txt_initiate_process") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('addBaseLine')">
          <span class="jwi-iconflag" style="margin-right: 8px"></span>
          {{ $t("txt_add_to_baseline") }}
        </a-menu-item>
        <a-menu-item v-if="!loading && erp" @click="validSelect('sendErp')">
          <span class="jwi-iconinternet" style="margin-right: 8px"></span>
          发送ERP
        </a-menu-item>
        <a-menu-item v-if="!loading && erp" @click="validSelect('sendHZZ')">
          <span class="jwi-iconinternet" style="margin-right: 8px"></span>
          发送慧致造
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('setStatus')">
          <span class="jwi-iconstate-set" style="margin-right: 8px"></span>
          设置状态
        </a-menu-item>
        <a-menu-item
          v-if="!loading && isProduct"
          @click="validSelect('createProblem')"
        >
          <span class="jwi-iconinfo-circle" style="margin-right: 8px"></span>
          提交问题报告
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('changeProcess')">
          <span class="jwi-iconsubItem-add" style="margin-right: 8px"></span>
          发起变更申请
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('idsStatus')">
          <span
            class="jwi-iconattribute-revision"
            style="margin-right: 8px"
          ></span>
          撤销IDS系统状态
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('exportBOM')">
          <span class="jwi-iconexport" style="margin-right: 8px"></span>
          批量导出BOM
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('batchDeleteByMasterOid')">
          <span class="jwi-iconexport" style="margin-right: 8px"></span>
          批量删除迭代
        </a-menu-item>
      </a-menu>
    </a-dropdown>

    <select-list-table-dialog
      ref="select-list-table-dialog"
      :type="actionType"
      :selectList.sync="selectList"
      :visible.sync="showdialog"
      @batchOperator="batchOperator"
      @batchProcess="batchInitiateProcess"
      @batchBaseLine="batchAddBaseLine"
      @reloadpage="reloadpage"
    />

    <start-process-modal
      :visible.sync="processVisible"
      ref="start-process-modal"
      :pageCode="'processManage'"
      :typeList="listType"
      :detailInfo="{}"
      @close="processVisible = false"
      @getTableData="resetProcessModal"
    ></start-process-modal>

    <!-- 变更流程单独处理 -->
    <change-start-process-modal
      :visible.sync="changeprocessVisible"
      ref="change-start-process-modal"
      :pageCode="'processManage'"
      :typeList="listType"
      :detailInfo="{}"
      @close="changeprocessVisible = false"
      @getTableData="resetProcessModal"
    ></change-start-process-modal>

    <!-- 文档流程单独处理 -->
    <start-process-page
      ref="ref_start_process"
      :pageCode="'processManage'"
      :typeList="listType"
      @getTableData="resetProcessModal"
    ></start-process-page>

    <!-- 问题报告 -->
    <create-problem
      ref="ref_create_problem"
      :pageCode="'processManage'"
      :selectList.sync="selectList"
      :visible="visibleProblem"
      @close="onCloseModal"
      @getTableData="resetProcessModal"></create-problem>
  </div>
</template>

<script>
import Row_Store from "jw_stores/instance-info";
import SelectListTableDialog from "./batch-opertaorcomm/select-list-table-dialog.vue";
import StartProcessModal from "../product-content/process-manage/start-process-modal.vue";

import StartProcessPage from "../product-content/process-manage/start-process-page-new";

import ChangeStartProcessModal from "../product-content/process-manage/change-start-process-modal.vue";

import CreateProblem from "views/problem-manage/problem-list/create-problem";

import {
  batchMoveAction,
  batchSaveAs,
  batchScreenSaveAs,
  batchCreateThenStart,
  batchLink, findPartBom,
} from "./api/index";
export default {
  components: {
    SelectListTableDialog,
    StartProcessModal,
    ChangeStartProcessModal,
    StartProcessPage,
    CreateProblem,
  },
  props: {
    selectList: {
      type: Array,
      default: () => [],
    },
    containerOid: {
      type: String,
    },
    // containerModel: {
    //   type: String
    // },
    containerType: {
      type: String,
    },
    containerModelDefinition: {
      type: String,
    },
    erp: {
      type: Boolean,
      default: false,
    },
    isProduct: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      showdialog: false,
      actionType: "",
      processVisible: false,
      changeprocessVisible: false,
      visibleProblem: false,
    };
  },
  watch: {
    selectList: function (val) {
      console.log("选择项改变", val);
    },
  },
  computed: {
    listType: {
      get() {
        return new Set(
          this.selectList.map((item) => item.modelDefinition || item.type)
        );
      },
      set(val) {},
    }
  },
  methods: {
    //清除默认选择数据
    clearSelect() {
      this.$emit("update:selectList", []);
    },
    //列表弹窗loading开启
    listloading() {
      this.$refs["select-list-table-dialog"].btnloading = true;
    },
    //列表弹窗loading关闭
    listcloseloading() {
      this.$refs["select-list-table-dialog"].btnloading = false;
    },
    //验证是否有选择项
    validSelect(actionType) {
      if (this.selectList && this.selectList.length > 0) {
        this.actionType = actionType;
        //验证通过打开弹窗
        if (actionType === "process") {
          //文档流程单独处理
          let rows = JSON.parse(JSON.stringify(this.selectList));
          let currentRow = rows[0];
          console.log(currentRow.masterType, "currentRow.masterType");
          if (currentRow.masterType == "Document") {
            this.$refs.ref_start_process.show({
              currentRow,
              defaultTableList: rows,
            });
            return;
          }
          //发起流程弹出其他页面
          this.$refs["start-process-modal"].defaultTableList = JSON.parse(
            JSON.stringify(this.selectList)
          );
          this.processVisible = true;
        } else if (actionType === "changeProcess") {
          let idsDoc = this.selectList.find((item) => {
            return item.number.includes("IDS");
          });
          if (idsDoc) {
            return this.$error(
                `${idsDoc.number}为IDS类数据，不能执行变更操作`
            );
          }
          let noRel = this.selectList.find((item) => {
            return item.lifecycleStatus != "Released" && item.lifecycleStatus != "released";
          });
          if(this.selectList.length>=2){
            let d=this.selectList.filter(ele=>ele.type==='DocumentIteration')
            if(d.length!==this.selectList.length&&d.length){
              return this.$error('文档变更不允许与其他对象一起进行')
            }
          }
          if (noRel) {
            return this.$error(
              `${noRel.number}数据状态为未发布，不能执行变更操作`
            );
          }

          Row_Store.set("changeRows", this.selectList);
          //获取容器的oid, modelDefinition
          let { oid, modelDefinition } = this.$route.query;
          this.$router.push({
            path: `/change-management/ecr/create/${oid}`,
            query: {
              oid: this.containerOid || oid,
              modelDefinition: this.containerModelDefinition || modelDefinition,
            },
          });
        } else if (actionType === "setStatus") {
          if (this.checkStatus()) {
            this.showdialog = true;
          } else {
            this.$warning("请选择状态相同的数据");
          }
        } else if (actionType === "createProblem") {
          //问题报告
          let noRel = this.selectList.find((item) => {
            return item.lifecycleStatus != "Released" && item.lifecycleStatus != "released";
          });
          if (noRel) {
            return this.$error(
              `${noRel.number}数据状态为未发布，不能提交问题报告`
            );
          }
          this.visibleProblem = true;
        } else if (actionType === "process_wf") {

          // 外发文件 - 创建副本避免直接修改prop
          let processRows = JSON.parse(JSON.stringify(this.selectList));
          processRows.forEach(item => {
            item.btnType = '外发BOM'; // 为每个对象添加 btnType 属性并赋值
          });

          // 监测所有对象的 lifecycleStatus 是否为 "Released"
          const hasUnreleased = this.selectList.some(item => item.lifecycleStatus !== "Released");

          if (hasUnreleased) {
            return this.$error(`外发BOM所有选中对象的生命周期状态必须为 已发布！`);
          }

          let currentRow = processRows[0];
          console.log(currentRow.masterType, "currentRow.masterType");
          //Part、Document、ECAD、MCAD流程单独处理
          if (["Document", "Part", "ECAD", "MCAD"].includes(currentRow.masterType)) {
            this.$refs.ref_start_process.show({
              currentRow,
              defaultTableList: processRows,
            });
            return;
          }
          console.log('外发BOM' + this.selectList);

          // 发起流程弹出其他页面
          this.$refs["start-process-modal"].defaultTableList = processRows;
          this.processVisible = true;
        }
        else {
          this.showdialog = true;
        }
        return true;
      } else {
        this.$warning(this.$t("txt_pls_data"));
        return false;
      }
    },
    checkStatus() {
      let flag = true;
      if (this.selectList.length === 1) {
        return flag;
      }
      this.selectList.reduce((a, b) => {
        if (a.lifecycleOid === b.lifecycleOid) {
          flag = true;
        } else {
          flag = false;
        }
        return b;
      });
      return flag;
    },
    resetProcessModal() {
      this.$emit("reloadpage");
    },
    reloadpage() {
      // 清除选中数据并触发页面刷新
      this.clearSelect();
      this.$emit("reloadpage");
    },
    //批量操作
    batchOperator(selectList, treeData, splitcode, beforcode, currentStage) {
      const names = selectList.map((item) => {
        return {
          sourceOid: item.oid,
          newName: item.newname
            ? item.newname
            : (beforcode ? beforcode : "") + item.name + splitcode,
        };
      });
      switch (this.actionType) {
        case "move":
          //批量移动
          this.batchMoveExecute(selectList, treeData);
          break;
        case "forming":
          this.batchScreenSave(
            "forming",
            "Part",
            selectList,
            treeData,
            splitcode,
            beforcode,
            currentStage
          );
          break;
        case "screen":
          console.log("selectList3333", selectList);
          this.batchScreenSave(
            "screen",
            "Part",
            selectList,
            treeData,
            splitcode,
            beforcode,
            currentStage
          );
          break;
        case "changestage":
        case "saveAs":
          //批量另存为
          let paramObj = this.batchOperatorType(selectList);
          for (const param in paramObj) {
            if (Object.hasOwnProperty.call(paramObj, param)) {
              const element = paramObj[param];
              this.batchOtherSave(
                param,
                element,
                treeData,
                splitcode,
                beforcode,
                currentStage
              );
            }
          }
          break;
        default:
          break;
      }
    },

    //按不同类型执行移动
    batchMoveExecute(list, tree) {
      this.listloading();
      batchMoveAction(list, tree)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
          //重新加载行
          this.$emit("reloadpage");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.listcloseloading();
        });
    },

    //根据不同类型分类
    batchOperatorType(selectList) {
      let res = {};
      selectList.forEach((item) => {
        if (res[item.masterType]) {
          res[item.masterType].push(item);
        } else {
          res[item.masterType] = [item];
        }
      });
      console.log(res, "res");
      if (!res["MCAD"]) {
        res["MCAD"] = [];
      }
      if (!res["Part"]) {
        res["Part"] = [];
      }
      if (!res["ECAD"]) {
        res["ECAD"] = [];
      }
      if (!res["Document"]) {
        res["Document"] = [];
      }

      res["MCAD"] = [
        ...res["MCAD"],
        ...res["Part"],
        ...res["ECAD"],
        ...res["Document"],
      ];
      delete res["Part"];
      delete res["ECAD"];
      delete res["Document"];
      return res;
    },

    //批量另存为
    batchOtherSave(type, list, treeData, splitcode, beforcode, currentStage) {
      this.listloading();
      batchSaveAs(type, list, treeData, splitcode, beforcode, currentStage)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
          //重新加载行
          this.$emit("reloadpage");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    batchScreenSave(
      operationType,
      type,
      list,
      treeData,
      splitcode,
      beforcode,
      currentStage
    ) {
      this.listloading();
      batchScreenSaveAs(
        operationType,
        type,
        list,
        treeData,
        splitcode,
        beforcode,
        currentStage
      )
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
          //重新加载行
          this.$emit("reloadpage");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    //批量发起流程
    batchInitiateProcess(selectList, val) {
      this.listloading();
      this.$refs["select-list-table-dialog"].processVisible = false;
      batchCreateThenStart(selectList, val, {
        catalogOid: this.containerOid,
        catalogType: this.containerType,
        containerOid: this.containerOid,
        containerType: this.containerType,
      })
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.clearSelect();
          this.showdialog = false;
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    //批量添加至基线
    batchAddBaseLine(oid, selectList) {
      this.listloading();
      batchLink(oid, selectList)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
        })
        .catch((e) => {
          console.log(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    onCloseModal() {
      this.visibleProblem = false;
    },
  },
};
</script>

<style lang="less" scoped>
</style>
