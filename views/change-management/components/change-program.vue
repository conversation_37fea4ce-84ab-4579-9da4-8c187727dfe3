<!--
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-08 15:27:27
 * @LastEditTime: 2022-05-06 17:21:17
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-flex-row class="change-program" :leftWidth="414" :marginRight="0">
    <div class="jw-height left-panel" slot="left">
      <div class="left-panel-header">
        <p class="header-title">{{$t('change_obj')}}</p>
        <a-radio-group class="type-radio" v-model.trim="currentType" @change="onTypeChange" size="small">
          <a-radio-button :value="type.key" v-for="type in typeList" :key="type.key">
            <a-tooltip placement="top" :title="type.title">
              <jw-icon :type="type.icon" v-if="type.icon" />
              <template v-else>{{type.key}}</template>
            </a-tooltip>
          </a-radio-button>
        </a-radio-group>
      </div>
      <obj-list :current="currentObj" :data="showObjList" @change="onCurrentChange" v-loading="leftLoading" />
    </div>
    <div slot="middle" class="jw-height right-panel" v-loading="rightLoading">
      <a-tabs class="jw-tabs" v-model.trim="activeTab" @change="onTabChange" v-if="currentObj.changeScheme">
        <a-tab-pane class="jw-height" key="property" :tab="$t('txt_property')">
          <jwLayoutBuilder class="create-form" ref="layout" :layoutName="isEdit&&currentObj.type!='ECADIteration'&&currentObj.type!='MCADIteration'?'update':'show'" :modelName="currentObj.modelDefinition" :instanceData="currentObj.attributeChange" @initModel="onLayoutInit" @change="onLayoutChange" v-if="currentObj.attributeChange" :key="timeTemp">
            <template #createBy>
                <user-info :accounts="[currentObj.attributeChange.createBy]" :showname="true"></user-info>
            </template>
            <template #updateBy>
                <user-info :accounts="[currentObj.attributeChange.updateBy]" :showname="true"></user-info>
            </template>
            <template #owner>
                <user-info :accounts="[currentObj.attributeChange.owner]" :showname="true"></user-info>
            </template>
            <template #processInstanceId>
              <a v-if="currentObj.attributeChange.extensionContent.processInstanceId" :href="getDingUrl(currentObj.attributeChange.extensionContent.processInstanceId)">流程详情</a>
            </template>

            <!-- 自定义 source 字段模板，强制禁用下拉选择器 -->
            <template #source="{ formData }">
              <a-select :value="formData.source" disabled style="width: 100%; background-color: #f5f5f5; cursor: not-allowed;">
                <a-select-option :value="formData.source" v-if="formData.source">
                  {{ formData.source }}
                </a-select-option>
              </a-select>
            </template>

            <!-- 自定义 classificationInfo 字段模板，强制禁用 -->
            <template #classificationInfo="{ formData }">
              <a-select :value="formData.classificationInfo && formData.classificationInfo.displayName ? formData.classificationInfo.displayName : ''" disabled style="width: 100%; background-color: #f5f5f5; cursor: not-allowed;">
                <a-select-option :value="formData.classificationInfo && formData.classificationInfo.displayName ? formData.classificationInfo.displayName : ''" v-if="formData.classificationInfo && formData.classificationInfo.displayName">
                  {{ formData.classificationInfo.displayName }}
                </a-select-option>
              </a-select>
            </template>

            <template #deliveryOid="{ formData,onUpdate}">
              <a-tree-select v-model="formData.extensionContent.deliveryOid" :treeData="deliverTree" treeDefaultExpandAll show-search :disabled=!isEdit
                             :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" allowClear :filterTreeNode="filterTreeNode"
                             @change="(value) => simpleDeliveryChange(value)">

                <div slot="title" slot-scope="{ name, modelDefinition, modelIcon }">
                  <div
                      :class="['name-con', modelDefinition === 'Structure' || modelDefinition === 'Category' ? 'name-con-small' : 'name-con-big']">
                    <jwIcon :type="modelIcon"></jwIcon>
                    <div class="name-item">
                      {{ name }}
                    </div>

                  </div>
                </div>
              </a-tree-select>
            </template>
          </jwLayoutBuilder>
        </a-tab-pane>
        <a-tab-pane class="jw-height" key="structure" :tab="$t('txt_structure')" v-if="hasStructure(currentObj.type)" :forceRender="false">
          <Structure ref="structure" :isEdit="isEdit" :beforeData="currentObj" :objectDetailsData="currentObj.attributeChange" @init="onStructureInit" :key="currentObj.oid"> </Structure>
        </a-tab-pane>
        <!-- 有效性 -->
        <a-tab-pane :tab="$t('txt_effectivity')" key="effectivity" v-if="currentObj.attributeChange.genericType === 'variable'">
          <div class="effectivity-area" v-if="activeTab === 'effectivity' && currentObj && currentObj.attributeChange">
            <effectivity :recorddata="currentObj.attributeChange" :oldrecorddata="currentObj.attributeChange.beforeData" :editflag="isEdit" />
          </div>
        </a-tab-pane>
      </a-tabs>
      <empty v-else>{{$t('change_no_program')}}</empty>
    </div>
    <!-- 添加变更对象弹窗 -->
    <add-obj-dialog ref="addDialog" />
    <!-- 工艺规程详情抽屉 -->
    <!-- <plan-drawer ref="planDrawer" /> -->
    <!-- 工序详情抽屉 -->
    <!-- <process-drawer ref="processDrawer" /> -->
    <!-- 工步详情抽屉 -->
    <!-- <sequence-drawer ref="sequenceDrawer" /> -->
    <DialogStockValue ref="transfer" :visibleShow="visibleStack" @close="cancelData" :showObjList="showObjList">
    </DialogStockValue>

  </jw-flex-row>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwLayoutBuilder, jwFlexRow } from "jw_frame";
import {
  findChangeInfoApi,
  findChangeSchemeApi,
  createChangeSchemeApi,
  findChangeSchemeCompareApi
} from "apis/change";
import { findEcoChangeInfoApi } from "apis/change/eco";
import { findEcaChangeInfoApi } from "apis/change/eca";
import ObjList from "./obj-list.vue";
import AddObjDialog from "./add-obj-dialog.vue";
import Structure from "./structure";
import effectivity from "views/product-container/object-details/effectivity";
// import PlanDrawer from './plan-drawer.vue'
// import ProcessDrawer from './process-drawer.vue'
// import SequenceDrawer from './sequence-drawer.vue'
import Empty from "components/empty";
import DialogStockValue from "./dialog-stock-value";
import userInfo from "components/user-info";

// 标记值
import {
  DEL_FLAG_VALUE,
  MODIFY_FLAG_VALUE,
  ADD_FLAG_VALUE
} from "./flag-config.js";

// 更新Part对象详情
const updatePartDetail = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/updateChangeScheme`,
  method: "post"
});

// 更新Doc对象详情
const updateDocDetail = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.docMicroServer}/document/updateChangeScheme`,
  method: "post"
});

const fetchDeliveryFuzzy = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/delivery/querySelectDeliveryTree`,
  method: 'get',
});

// 更新变更对象基本信息api
const updateApiMap = {
  PartIteration: updatePartDetail,
  DocumentIteration: updateDocDetail
  // ParamIteration:updateParamApi,
  // PartIteration:updatePartApi,
  // DocumentIteration:updateDocumentApi,
  // OperationIteration:updateOperationApi
};

// 获取变更对象列表api
const apiMaps = {
  ECR: findChangeInfoApi,
  ECO: findEcoChangeInfoApi,
  ECA: findEcaChangeInfoApi
};

export default {
  name: "changeProgram",
  props: {
    changeObjs: Array, // 传入变更对象列表
    changeDetail: Object, // 外部 eca eco ecr 详情
    isEdit: Boolean, // 可编辑
    type: {
      // 类型 ECR ECO ECA
      type: String,
      default: "ECR"
    }
  },
  components: {
    ObjList,
    Empty,
    jwLayoutBuilder,
    AddObjDialog,
    jwFlexRow,
    Structure,
    // PlanDrawer,
    // ProcessDrawer,
    // SequenceDrawer,
    effectivity,
    DialogStockValue,
    userInfo
  },
  provide() {
    return {
      notifyUpdate: () => {},
      isEdit: this.isEdit
    };
  },
  data() {
    return {
      formRules: {
        'extensionContent.deliveryOid': [
          { required: true, message: '请选择交付项', trigger: 'change' }
        ]
      },
      delFlag: DEL_FLAG_VALUE,
      typeList: [
        {
          key: "All",
          title: this.$t("txt_all")
        },
        {
          key: "Part",
          title: this.$t("txt_part"),
          icon: "#jwi-lianjian",
          children: ["PartIteration"]
        },
        {
          key: "Document",
          title: this.$t("txt_doc"),
          icon: "#jwi-wendang",
          children: ["DocumentIteration"]
        },
        {
          key: "ECAD",
          title: "ECAD",
          icon: "#jwi-ecad",
          children: ["ECADIteration"]
        },
        {
          key: "MCAD",
          title: "MCAD",
          icon: "#jwi-mcad",
          children: ["MCADIteration"]
        }
      ],
      activeTab: "property",
      objList: [],
      showObjList: [],
      tableData: [],
      currentType: "All",
      currentObj: {},
      rightLoading: false,
      leftLoading: false,
      updateAble: false,
      initExpandKeys: [],
      visibleStack: false,
      deliverTree:[],
      timeTemp:Date.now(),
      disableFieldsExecuted: false, // 防止重复执行禁用操作
      disableCheckCount: 0 // 记录禁用检查次数
    };
  },
  computed: {
    columns() {
      return [
        {
          field: "name",
          title: this.$t("txt_name"),
          treeNode: true,
          width: 400,
          slots: {
            default: "name"
          }
        },
        {
          field: "number",
          title: this.$t("txt_number")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_status")
        },
        {
          field: "type",
          title: this.$t("txt_type")
        },
        // {
        //   field:'displayVersion',
        //   title:'版本'
        // },
        {
          field: "useRelDto",
          title: this.$t("txt_property_val"),
          slots: {
            default: ({ row }) => {
              const isChange = row.flag === MODIFY_FLAG_VALUE;
              const isAdd = row.flag === ADD_FLAG_VALUE;
              const { value, newValue } = row;
              return [
                //变更对应 新值 newValue 老值 value  新增对应 新值 newValue 老值 value
                isChange ? (
                  <p>
                    <span class="old-value">{value}</span> {newValue}
                  </p>
                ) : isAdd ? (
                  newValue
                ) : (
                  value
                )
              ];
            }
          }
        },
        {
          field: "unit",
          title: this.$t("txt_unit")
        }
      ];
    }
  },
  created() {

    this.fetchObjList();
    this.delayFetchUpdate = _.debounce(this.fetchUpdate, 300);
  },
  watch: {
    changeObjs(val) {
      this.initObjList(val);
    }
  },
  methods: {
    simpleDeliveryChange(value) {
      // 直接更新数据
      this.currentObj.attributeChange.extensionContent.deliveryOid = value || '';

    },
    getStockValue() {
      this.visibleStack = true;
    },
    cancelData() {
      this.visibleStack = false;
    },

    // 初始化变更对象
    async initObjList(list) {
      let noSchemeList= list.filter(item=>{
        return !item.changeScheme
      })
      if(noSchemeList.length){
        let api=[]
        noSchemeList.forEach(item=>{
          api.push(createChangeSchemeApi.execute({ oid:item.oid, type:item.type }))
        })
        await Promise.all(api).then(res=>{
          noSchemeList.forEach(item=>item.changeScheme = true)
        }).catch(err=> {
          return this.$error("批量创建变更方案异常")
        })
      }

      this.objList = list.map(item => {
        return {
          ...item,
          attributeChange: null,
          structureChange: null
        };
      });
      this.showObjList = this.objList;
      if (this.objList.length && !this.currentObj.oid) {
        // 默认高亮第一个变更对象
        this.setCurrent(this.objList[0]);
      }
    },
    onStructureInit() {},
    async notifyUpdate() {
      this.currentObj.structureChange = await this.fetchObjstructure(
        this.currentObj
      );
    },
    showDetail(row) {
      // 展示变更方案对象详情 打开抽屉
      if (this.disabledLink(row)) return;
      const topData = this.currentObj.structureChange[0];
      // if(row.type==='ProcessPlanIteration'){ // 工艺规程
      this.$refs.planDrawer
        .show({
          instanceData: topData,
          currentData: row,
          parentData: this.$refs.table.getParentRow(row),
          type: topData.type === "ProcessPlanIteration" ? "plan" : "process"
        })
        .then(() => {
          this.notifyUpdate(); // 关闭时重新获取结构信息
        });
    },
    showAdd() {
      this.$refs.addDialog.show();
    },
    onTypeChange() {
      let objList = [];
      const typeObj = this.typeList.find(item => item.key === this.currentType);
      if (!typeObj.children) {
        objList = this.objList;
      } else {
        objList = this.objList.filter(item =>
          typeObj.children.includes(item.type)
        );
      }
      if (objList.length) {
        this.setCurrent(objList[0]);
      } else {
        this.setCurrent(null);
      }
      this.showObjList = objList;
    },
    onCurrentChange(item) {
      this.timeTemp=Date.now()
      // 切换变更对象时 校验layout
      this.validteLayout().then(() => {
        this.setCurrent(item);
      });
    },
    async setCurrent(obj) {
      // 切换变更对象
      if (obj) {
        this.activeTab = "property";
        if (!obj.changeScheme) {
          // 未创建方案时需创建方案
          await this.fetchCreateProgram(obj);
          obj.changeScheme = true;
        }
        if (!obj.attributeChange) {
          // 未请求过详情时需请求详情
          this.rightLoading = true;
          obj.attributeChange = await this.fetchChangeScheme(obj);
          if (!obj.attributeChange) {
            this.$error(`${obj.name}${this.$t("change_error_parogram")}`);
          }
          this.rightLoading = false;
        }
        if (obj.structureChange && obj.structureChange.length) {
          // 切换变更对象后 需重新设置结构树默认展开
          this.initExpandKeys = [obj.structureChange[0].uniqueOid];
        }
        this.currentObj = obj;
      } else {
        this.currentObj = {
          attributeChange: null,
          structureChange: null
        };
        this.activeTab = "property";
      }
    },
    fetchObjList() {
      let api = apiMaps[this.type];
      api.execute({ oid: this.changeDetail.oid, list: [] }).then(res => {
        this.initObjList(res);
      });
    },
    fetchChangeScheme({ oid, type }) {
      this.leftLoading = true;
      let api = findChangeSchemeApi;
      if (!this.isEdit) {
        api = findChangeSchemeCompareApi;
      }
      return api
        .execute({ oid, type })
        .then(res => {
          this.leftLoading = false;
          if("DocumentIteration" === type) {
            // 变更单编辑与查看页面接口返回值不一致。进行判断处理
            if (this.isEdit) {
              if (res) {
                this.loadDeliver(res);
              }
            } else if (res && res.source) {
              this.loadDeliver(res.source);
            }
          }
          if (!this.isEdit) {
            const { source, contrast } = res;
            contrast.beforeData = source;
            return contrast;
          }
          return res;
        })
        .catch(err => {
          this.$error(err.msg);
          this.leftLoading = false;
        });
    },
    fetchObjstructure(obj) {
      this.leftLoading = true;
      const params = {
        source: { oid: obj.oid, type: obj.type },
        contrast: {
          oid: obj.attributeChange.oid,
          type: obj.attributeChange.type
        }
      };
      return getStructureDataApi
        .execute(params)
        .then(res => {
          this.leftLoading = false;
          if (!res) {
            return [];
          }
          let structure = [res];
          this.$XEUtils.eachTree(
            structure,
            (item, index, items, path, parent) => {
              // 手动设置唯一值
              item.uniqueOid = parent ? parent.uniqueOid + item.oid : item.oid;
            }
          );
          return [res];
        })
        .catch(err => {
          this.$error(err.msg);
          this.leftLoading = false;
          return [];
        });
    },
    fetchCreateProgram({ oid, type }) {
      return createChangeSchemeApi.execute({ oid, type });
    },
    hasStructure(type) {
      // Part有结构tab
      return ["PartIteration"].includes(type);
    },
    validteLayout() {
      return new Promise((resolve, reject) => {
        if (this.$refs.layout) {
          this.$refs.layout.validate().then(
            () => {
              resolve(true);
            },
            () => {
              reject(false);
            }
          );
        } else {
          resolve(true);
        }
      });
    },
    async onTabChange() {
      if (this.activeTab === "structure") {
        // 切换到tab时
        // if(!this.currentObj.structureChange){ // 未请求结构信息需初始化
        //   this.rightLoading=true
        //   this.currentObj.structureChange=await this.fetchObjstructure(this.currentObj) // 缓存起来
        //   this.rightLoading=false
        // }
        // if(this.currentObj.structureChange[0]){ // 结构树默认展开顶点
        //   this.initExpandKeys=[this.currentObj.structureChange[0].uniqueOid]
        // }
      }
    },
    onLayoutInit({ layout }) {
      // 禁用 source 和 classificationInfo 属性
      if (layout && layout.content && layout.content.layout) {
        let rows = layout.content.layout;

        rows.forEach(row => {
          row.forEach(item => {
            // 检查多种可能的字段名
            const fieldName = item.fieldName;
            const shouldDisable = fieldName === "source" ||
                                fieldName === "extensionContent.source" ||
                                fieldName === "classificationInfo" ||
                                fieldName === "extensionContent.classificationInfo" ||
                                fieldName.includes("source") ||
                                fieldName.includes("classificationInfo");

            if (shouldDisable) {
              console.log('Layout level - Disabling field:', fieldName);
              // 多种方式设置禁用状态
              item.disabled = true;
              item.readonly = true;
              item.editable = false;
              this.$set(item, 'disabled', true);
              this.$set(item, 'readonly', true);
              this.$set(item, 'editable', false);
            }
          });
        });
      }

      // 重置禁用检查计数器
      this.disableFieldsExecuted = false;
      this.disableCheckCount = 0;

      // 延迟更新 防止layout初始化时 change事件 调用更新接口
      this.updateAble = false;
      setTimeout(() => {
        this.updateAble = true;
        // 再次检查并强制禁用
        this.forceDisableFields();
      }, 500);
    },

    // 强制禁用字段的方法
    forceDisableFields() {

      if (this.$refs.layout && !this.disableFieldsExecuted && 'PartIteration' === this.currentObj.type) {

        this.disableFieldsExecuted = true; // 防止重复执行

        // 尝试通过 DOM 操作禁用字段
        this.$nextTick(() => {
          // 查找所有表单项
          const formItems = document.querySelectorAll('.ant-form-item');
          let disabledCount = 0;

          formItems.forEach(formItem => {
            // 检查是否已经被禁用过
            if (formItem.classList.contains('ant-form-item-disabled')) {
              disabledCount++;
              return; // 跳过已经禁用的项
            }

            const label = formItem.querySelector('.ant-form-item-label');
            if (label) {
              const labelText = label.textContent || label.innerText || '';

              // 检查标签文本是否包含目标字段（添加"料品形态"）
              if (labelText.includes('料品形态') || labelText.includes('分类') ||
                  labelText.includes('classificationInfo') || labelText.includes('来源') ||
                  labelText.includes('source') || labelText.includes('Source')) {

                console.log('Disabling form item with label:', labelText);
                disabledCount++;

                // 查找该表单项内的所有输入元素
                const inputs = formItem.querySelectorAll('input, select, textarea');
                const selects = formItem.querySelectorAll('.ant-select');

                // 禁用输入框
                inputs.forEach(input => {
                  input.disabled = true;
                  input.readonly = true;
                  input.style.backgroundColor = '#f5f5f5';
                  input.style.cursor = 'not-allowed';
                });

                // 禁用下拉选择器
                selects.forEach(select => {
                  select.classList.add('ant-select-disabled');
                  select.style.backgroundColor = '#f5f5f5';
                  select.style.cursor = 'not-allowed';
                  select.style.pointerEvents = 'none';

                  // 也禁用选择器内部的元素
                  const selector = select.querySelector('.ant-select-selector');
                  if (selector) {
                    selector.style.backgroundColor = '#f5f5f5';
                    selector.style.cursor = 'not-allowed';
                  }
                });

                // 为整个表单项添加禁用样式
                formItem.classList.add('ant-form-item-disabled');
                formItem.style.opacity = '0.6';
              }

            }
            //2025-08-20 PCN的变更流程中 禁止掉所有part类型的属性编辑

            const inputs = formItem.querySelectorAll('input, select, textarea');
            const selects = formItem.querySelectorAll('.ant-select');

            // 禁用输入框
            inputs.forEach(input => {
              input.disabled = true;
              input.readonly = true;
              input.style.backgroundColor = '#f5f5f5';
              input.style.cursor = 'not-allowed';
            });

            // 禁用下拉选择器
            selects.forEach(select => {
              select.classList.add('ant-select-disabled');
              select.style.backgroundColor = '#f5f5f5';
              select.style.cursor = 'not-allowed';
              select.style.pointerEvents = 'none';

              // 也禁用选择器内部的元素
              const selector = select.querySelector('.ant-select-selector');
              if (selector) {
                selector.style.backgroundColor = '#f5f5f5';
                selector.style.cursor = 'not-allowed';
              }
            });

            // 为整个表单项添加禁用样式
            formItem.classList.add('ant-form-item-disabled');
            formItem.style.opacity = '0.6';
          });

          // 如果找到了目标字段并且都已经禁用，就不再重复执行
          if (disabledCount >= 2) { // 假设有2个目标字段需要禁用
            console.log('All target fields disabled, stopping further checks');
            return;
          }

          // 只在前几次执行延迟检查，避免无限循环
          if (!this.disableCheckCount) {
            this.disableCheckCount = 0;
          }
          this.disableCheckCount++;

          if (this.disableCheckCount < 3) { // 最多检查3次
            setTimeout(() => {
              this.disableFieldsExecuted = false; // 重置标志，允许再次执行
              this.forceDisableFields();
            }, 1000);
          } else {
            console.log('Disable fields check completed after', this.disableCheckCount, 'attempts');
          }
        });
      }
    },
    onLayoutChange(data) {
      if (this.isEdit && this.updateAble) {
        if(this.isUploading(data)) {
          return;
        }
        this.delayFetchUpdate(data);
      }
    },
    fetchUpdate(data) {
      this.validteLayout().then(() => {
        const api = updateApiMap[data.type];
        api
          .execute({ ...data, change: true })
          .then(resp => {
            this.fetchObjList();
          })
          .catch(err => {
            this.$error(err.msg);
          });
      });
    },
    isUploading(data) {
      let needReturn = false;
      if (data?.primaryFile?.length > 0 && data?.primaryFile[0]?.status === "uploading") {
        this.$parent.$parent.$parent.$parent.$parent.$parent.disableNext = true;
        return true
      }
      if(data?.secondaryFile) {
        data.secondaryFile.forEach(item=>{
          if(item?.status === "uploading") {
            this.$parent.$parent.$parent.$parent.$parent.$parent.disableNext = true;
            needReturn = true;
            return true
          }
        })
      }
      if(needReturn) {
        return true;
      }
      this.$parent.$parent.$parent.$parent.$parent.$parent.disableNext = false;
      return false;
    },
    // 行类名，区分差异用
    rowClassName({ row }) {
      const { flag } = row;
      if (flag === DEL_FLAG_VALUE) {
        return "del";
      } else if (flag === MODIFY_FLAG_VALUE) {
        return "modify";
      } else if (flag === ADD_FLAG_VALUE) {
        return "add";
      } else {
        return "";
      }
    },
    disabledLink(row) {
      return this.isEdit && row.flag === DEL_FLAG_VALUE;
    },
    getValue() {
      return { changeObjs: this.objList };
    },
    getDingUrl(instanceId) {
      let url = `https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid=${Jw.dingCorPid}&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId=${instanceId}&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval`
      return `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(url)}&pc_slide=true`
    },
    filterTreeNode(inputVal, treeNode){
      return treeNode.data.props.name.indexOf(inputVal) !== -1
    },
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "title" };
      let arr = [];
      const loop = (tree) => {
        tree.forEach(item => {
          // 设置节点的基本属性
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;
          delete item.root;

          // 禁用父节点的点击选择
          if (item.children && item.children.length > 0) {
            item.disabled = true; // 禁用父节点点击选择
            loop(item.children); // 递归处理子节点
          }
        });
      };
      loop(data);
      return onlyDirectChildren ? arr : data;
    },
    loadDeliver(objectDetailsData) {
      if(objectDetailsData) {
        fetchDeliveryFuzzy.execute({
          containerOid: objectDetailsData.containerOid,
        }).then(resp => {
          this.deepData(resp)
          this.deliverTree = resp
        })
      }
    }
  }
};
</script>

<style lang="less" scoped>
.left-panel {
  display: flex;
  flex-direction: column;
  padding: 10px;
}
.left-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
}
.change-program {
  background-color: transparent;
  /deep/.column-middle {
    // padding: 0 16px 16px;
  }
}
.structure-link {
  cursor: pointer;
  color: @primary-color;
  margin-left: 5px;
  &.disabled {
    color: #bfa8a8;
    cursor: not-allowed;
  }
}
.type-radio {
  /deep/.ant-radio-button-wrapper {
    height: 28px;
    line-height: 28px;
  }
}
.header-title {
  color: @heading-color;
}
.add-btn {
  width: 285px;
  margin: 0 auto;
}

.right-panel {
  position: relative;
}
.right-table {
  padding: 15px;
  /deep/.vxe-body--row {
    &.add {
      background: @jw-block-add-bg;
    }
    &.modify {
      background: @jw-block-modify-bg;
    }
    &.del {
      background: @jw-block-del-bg;
    }
  }
}
.create-form {
  width: 704px;
  margin: 15px auto 0;
}
.old-value {
  color: @disabled-color;
  text-decoration: line-through @warning-color 2px;
}
.effectivity-area {
  padding: 15px;
}

.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}

.name-con {
  display: flex;
  align-items: center;

  .name-item {
    margin: 0 8px;
    cursor: pointer;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 禁用字段的样式
.ant-form-item-disabled {
  .ant-form-item-control-input {
    .ant-input,
    .ant-select,
    .ant-select-selector {
      background-color: #f5f5f5 !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
    }
  }
}

.ant-select-disabled {
  .ant-select-selector {
    background-color: #f5f5f5 !important;
    cursor: not-allowed !important;
  }
}
</style>
