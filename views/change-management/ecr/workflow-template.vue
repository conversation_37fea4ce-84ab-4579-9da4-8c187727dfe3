<template>
  <div class="workflow-template-wrap">
    <div class="workflow-template-list">
      <a-input-search v-model.trim="searchKey" class="search-input" allow-clear :placeholder="$t('search_text')" @search="onSearch" />
      <div class="radio-wrap">
        <a-radio-group v-model.trim="selectWorkflow" @change="onChangeRadio">
          <a-radio v-for="item in workflowList" :key="item.key" :value="item">
            {{ item.name }}
          </a-radio>
        </a-radio-group>
      </div>
    </div>
    <div v-if="!_.isEmpty(selectWorkflow)" class="workflow-template-info">
      <div class="right-title">{{ selectWorkflow.name }}
        <span class="right-tip"> {{ selectWorkflow.metaInfo }} </span>
      </div>

<!--      <div class="right-process-img">
        <img :src="workflowImageUrl" alt="" />
      </div>-->

      <!-- <div class="right-temp-wrap">
        <div>{{ $t("txt_team_temp") }}</div>
        <a-select v-model.trim="teamTemp" placeholder="请选择" show-search :filter-option="filterOption" allowClear @change="getRoleWithUser">
          <a-select-option v-for="val in teamTempOpts" :key="val.oid" :value="val.oid">
            {{ val.name }}
          </a-select-option>
        </a-select>
      </div> -->
      <!--   文档归档及函审   -->
<!--      <div v-if="selectWorkflow.key === 'cn_jwis_docbg'">
        <div class="formLable">是否函审</div>
        <a-select v-model="stepsInfo.additionaltranslation.isReview"  class="form-item">
          <a-select-option key="是">是</a-select-option>
          <a-select-option key="否">否</a-select-option>
        </a-select>
        <div class="formLable">是否外发</div>
        <a-select v-model="stepsInfo.additionaltranslation.isOutgoing"  class="form-item">
          <a-select-option key="是">是</a-select-option>
          <a-select-option key="否">否</a-select-option>
        </a-select>
      </div>-->

      <a-form-model :model="stepsInfo.additionaltranslation" >
        <div v-if="selectWorkflow.key === 'cn_jwis_docbg'">
          <a-form-model-item
              label="是否函审"
              prop="isReview"
              :rules="[{ required: true, message: '请选择是否函审' }]"
          >
            <a-select
                v-model="stepsInfo.additionaltranslation.isReview"
                placeholder="请选择"
                class="form-item"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item
              label="是否外发（包括自研单机方）"
              prop="isOutgoing"
              :rules="[{ required: true, message: '请选择是否外发' }]"
          >
            <a-select
                v-model="stepsInfo.additionaltranslation.isOutgoing"
                placeholder="请选择"
                class="form-item"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
      </a-form-model>

      <a-form-model :model="stepsInfo.additionaltranslation" >
        <div v-if="selectWorkflow.key === 'cn_jwis_qr'">
          <a-form-model-item label="更改内容说明" prop="changeDesc">
            <a-input v-model="stepsInfo.additionaltranslation.changeDesc"
                     class="form-item"
                     placeholder="请输入更改内容说明"
            ></a-input>
          </a-form-model-item>

          <!-- 工艺路线选择 -->
          <a-form-model-item
            label="工艺路线"
            prop="workdefinition"

          >
            <a-select
              v-model="smisData.selectedWorkdefinition"
              placeholder="请选择工艺路线"
              class="form-item"
              @change="onWorkdefinitionChange"
            >
              <a-select-option
                v-for="item in smisData.workdefinitions"
                :key="item"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <!-- 工单多选 -->
          <a-form-model-item
            label="工单"
            prop="workrequests"

          >
            <a-select
              v-model="smisData.selectedWorkrequests"
              mode="multiple"
              placeholder="请选择工单"
              class="form-item"
            >
              <a-select-option
                v-for="item in smisData.workrequests"
                :key="item.workrequestno"
                :value="item.workrequestno"
              >
                {{ item.workrequestno }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <!-- 工序多选 -->
          <a-form-model-item
            label="工序"
            prop="works"

          >
            <a-select
              v-model="smisData.selectedWorks"
              mode="multiple"
              placeholder="请选择工序"
              class="form-item"
            >
              <a-select-option
                v-for="item in smisData.works"
                :key="item.workno"
                :value="`${item.workno}-${item.workname}`"
              >
                {{ item.workno }}-{{ item.workname }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
      </a-form-model>

      <div class="right-handler-wrap" v-show="variableList.length > 0">
        <div class="handler-item" v-for="item in variableList" :key="item.name">
          <div class="item-head">
            {{ item.displayName }}
            <i class="jwi-iconrefresh"></i>
          </div>
          <div class="item-body">
            <div class="handler-add-btn" @click="onAddUser(item)">
              <i class="jwi-iconuser-add"></i>
            </div>
            <div class="handlers" v-show="!!item.users.length">
              <div class="handler" v-for="val in item.users" :key="val.account">
                <i class="jwi-iconclose-circle-full close-icon" @click="onDeleteUser(item.users, val)"></i>
                <div class="avatar">
                  <img v-if="val.avatar" :src="val.avatar" alt="" />
                  <a-avatar v-else>{{ val.name | filterName }}</a-avatar>
                </div>
                <div>{{ val.name }}（{{ val.account }}）</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <empty-com v-else></empty-com>
<!--    <jw-user-modal-v2 ref="ref_user_modal" :isCheckbox="ischeckBox" />-->
    <userModalV3 ref="ref_user_modal" :isCheckbox="true"/>
  </div>
</template>
<script>
import EmptyCom from "components/empty";
import { jwUserModalV2 } from "jw_frame";
import userModalV3 from "../../../components/user-modal-v3";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
import {bool} from "three/nodes";
// 查询已部署的最新流程模板
const fetchDeployed = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.workflowMicroServer
  }/workflow/model/deployed/latest/fuzzy`,
  method: "get"
});

/**
 * 获取模型流程模板
 */
const findWorkflowApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findWorkflow`,
  method: "get"
});

// 获取需要配置的角色
const fetchVariables = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.workflowMicroServer
  }/workflow/repository/start/variables`,
  method: "get"
});

// 获取角色对应的用户
const fetchRoleWithUser = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.accountMicroServer
  }/team/templaterole/search/roleWithUser`,
  method: "get"
});

// 获取团队
const fetchTeam = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/search/cp/keyword/page`,
  method: "post"
});

//自动填充人员
const findAutoRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/process/team/autoUser`,
  method: "post"
});

// 创建并启动流程
const createThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/customer/ding/task/createSendTask`,
  method: "post"
});

//根据物料编码查询SMIS工艺路线
const findWorkRequestsFromSMIS = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/findWorkRequestsFromSMIS`,
  method: "get"
});

//根据物料编码和工单查询SMIS工序
const findWorksFromSMIS = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/findWorksFromSMIS`,
  method: "get"
});

export default {
  components: {
    EmptyCom,
    jwUserModalV2,
    userModalV3
  },
  props: {
    changeDetail: Object,
    changeObjs: Array
  },

  data() {
    this._startUser = Jw.getUser();
    return {
      searchKey: "",
      selectWorkflow: null,
      ischeckBox: true,
      workflowImageUrl: "",
      teamTemp: undefined,
      teamTempOpts: [],
      variableList: [],

      workflowList: [],
      stepsInfo: {
        additionaltranslation: {
          isReview: null,
          isOutgoing: null,
          changeType: "",         // 更改类型
          changeDesc: "",         // 更改类型
          changeReason: "",       // 更改原因
          changePlan: "",          // 更改方案
          affectedObjects: "",     // 更改受影响对象
          impactAnalysis: "",      // 更改影响分析
          changeAttachments: []    // 上传更改附件
        },
      },
      // SMIS相关数据
      smisData: {
        itemcode: "",
        workdefinitions: [],
        selectedWorkdefinition: "",
        workrequests: [],
        selectedWorkrequests: [],
        works: [],
        selectedWorks: []
      }
    };
  },
  async created() {
    await this.fetchWorkflow();
    this.getVariables();
    this.getTeam();
  },
  watch: {
    // 监听工作流变化，如果是cn_jwis_qr则获取工艺路线数据
    'selectWorkflow.key': {
      handler(newKey) {
        if (newKey === 'cn_jwis_qr') {
          this.fetchWorkRequestsFromSMIS();
        }
      },
      immediate: true
    }
  },
  computed: {},
  filters: {
    filterName: function(name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase();
      }
      return showName;
    }
  },
  methods: {
    // 获取工艺路线数据
    async fetchWorkRequestsFromSMIS() {
      if (!this.changeDetail || !this.changeDetail.oid) {
        return;
      }

      try {
        const res = await findWorkRequestsFromSMIS.execute({
          ecrOid: this.changeDetail.oid
        });

        if (res ) {
          this.smisData.itemcode = res.itemcode;
          this.smisData.workdefinitions = res.workdefinitions || [];


          // 如果有多个工艺路线，默认选中第一个
          if (this.smisData.workdefinitions.length > 0) {
            this.smisData.selectedWorkdefinition = this.smisData.workdefinitions[0];
            // 自动获取工序数据
            this.fetchWorksFromSMIS();
          }
        }
      } catch (err) {
        this.$error(err.msg || '获取工艺路线数据失败');
      }
    },

    // 获取工序数据
    async fetchWorksFromSMIS() {
      if (!this.smisData.itemcode || !this.smisData.selectedWorkdefinition) {
        return;
      }

      try {
        const res = await findWorksFromSMIS.execute({
          itemcode: this.smisData.itemcode,
          workdefinition: this.smisData.selectedWorkdefinition
        });

        if (res ) {
          this.smisData.works = res.works || [];
          this.smisData.workrequests = res.workrequests || [];
          // 清空之前选择的工序
          this.smisData.selectedWorks = [];
        }
      } catch (err) {
        this.$error(err.msg || '获取工序数据失败');
      }
    },

    // 工艺路线选择变化时的处理
    onWorkdefinitionChange() {
      // 清空工单和工序的选择
      this.smisData.selectedWorkrequests = [];
      this.smisData.selectedWorks = [];
      // 重新获取工序数据
      this.fetchWorksFromSMIS();
    },

    async checkIsReviewEcr() {
      const { isReview, isOutgoing } = this.stepsInfo.additionaltranslation;

      // 只有当工作流key为cn_jwis_docbg时才校验函审和外发字段
      if (this.selectWorkflow.key === 'cn_jwis_docbg') {
        if (!isReview) {
          this.$error('请填写是否函审');
          return false;  // 阻止流程继续执行
        }

        if (!isOutgoing) {
          this.$error('请填写是否外发');
          return false;  // 阻止流程继续执行
        }
      }

      return true;  // 表示检查通过
    },
    onStart() {
      // 如果是cn_jwis_qr工作流，需要验证SMIS相关字段
      //先不对这些进行校验
      let isCheckFlag = false;
      if (this.selectWorkflow.key === 'cn_jwis_qr' && isCheckFlag) {
        if (!this.smisData.selectedWorkdefinition) {
          this.$error('请选择工艺路线');
          return Promise.reject('请选择工艺路线');
        }
        if (!this.smisData.selectedWorkrequests || this.smisData.selectedWorkrequests.length === 0) {
          this.$error('请选择工单');
          return Promise.reject('请选择工单');
        }
        if (!this.smisData.selectedWorks || this.smisData.selectedWorks.length === 0) {
          this.$error('请选择工序');
          return Promise.reject('请选择工序');
        }
      }

      let {  additionaltranslation } = this.stepsInfo;
      let teamContent = [];
      for (let i = 0; i < this.variableList.length; i++) {
        const item = this.variableList[i];
        if (!item.users.length&&!item.countersignFlag) {
          this.$warning(this.$t("流程角色请选择用户"));
          return Promise.reject("流程角色请选择用户");
        }
        teamContent.push({
          roleName: item.name,
          name:item.displayName,
          users: item.users.map(val => val.account),
        });
      }

      let {
        catalogOid,
        catalogType,
        containerOid,
        containerType,
        containerModelDefinition
      } = this.changeDetail;

      // 构建基础参数
      let params = {
        locationInfo: {
          catalogOid,
          catalogType,
          containerOid,
          containerType,
          containerModelDefinition
        },
        modelDefinition: "ProcessOrder",

        bizObjects: [this.changeDetail],
        processModelId: this.selectWorkflow.id,
        teamContent,
        name: this.selectWorkflow.name,
        extensionContent:{
          docInfo:{},
          additionalTranslation: { ...this.stepsInfo.additionaltranslation }, // 添加附加信息
        },
        ...additionaltranslation
      };

      // 如果是cn_jwis_qr工作流，添加SMIS相关参数
      if (this.selectWorkflow.key === 'cn_jwis_qr') {
        params.extensionContent.smisData = {
          itemcode: this.smisData.itemcode,
          workdefinition: this.smisData.selectedWorkdefinition,
          workrequests: this.smisData.selectedWorkrequests,
          works: this.smisData.selectedWorks
        };
      }

      return createThenStartProcess
        .execute(params)
        .then(res => {
          this.$success(this.$t("msg_success"));
          return Promise.resolve(res);
        })
        .catch(err => {
          this.$error(err.msg);
          return Promise.reject(err.msg);
        });
    },

    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    getTeam() {
      fetchTeam
        .execute({
          containerOid: getCookie("tenantOid"),
          containerType: "Tenant",
          keyword: "",
          pageNum: 1,
          pageSize: 1000
        })
        .then(res => {
          this.teamTempOpts = res.rows;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    async validate() {
      if (this.$refs.ref_ht_doc) {
        await this.$refs.ref_ht_doc.validate();
      }
    },
    getValue() {
      let teamContent = this.variableList.map(item => {
        return {
          roleName: item.name,
          userAccounts: item.users.map(val => val.account)
        };
      });
      return {
        teamContent,
        selectWorkflow: this.selectWorkflow
      };
    },
    onChangeRadio() {
      this.teamTemp = undefined;
      this.variableList = [];
      this.workflowImageUrl = `${Jw.gateway}/${
        Jw.workflowMicroServer
      }/workflow/repository/process-definitions/image/byKey?key=${
        this.selectWorkflow.key
      }&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      this.getVariables();
    },
    onAddUser(item) {
      console.log("这是变更流程的11")
      this.$refs.ref_user_modal
        .show({
          type: "User"
        })
        .then(data => {
          console.log(data);
          // let oids = item.users.map(item => item.oid);
         /* data.forEach(val => {
            if (!oids.includes(val.oid)) {
              item.users.push(val);
            }
          });*/
          //2025-0226 修改为从IAM中获取用户
          let oids = item.users.map(item => item.userOid);
          data.forEach(val => {
            if (!oids.includes(val.userOid)) {
              item.users.push(val);
            }
          });
        });
    },
    onDeleteUser(list, item) {
      let index = list.indexOf(item);
      list.splice(index, 1);
    },
    findAutoRoleAct() {
      findAutoRole.execute(this.changeObjs).then(resp => {
        this.variableList.forEach(row => {
          let role = resp.find(item => item.name === row.name);
          if (role && _.isEmpty(row.users)) {
            row.users = role.users;
          }
        });
      });
    },
    getRoleWithUser() {
      if (!this.teamTemp) {
        this.variableList.forEach(item => {
          if(item.name==='owner'){
            item.users = [Jw.getUser()]
          }else{
            item.users = [];
          }
        });
        return;
      }
      fetchRoleWithUser
        .execute({
          teamTemplateOid: this.teamTemp
        })
        .then(res => {
          this.variableList.forEach(item => {
            const info = res.find(val => val.name == item.name);
            item.users = info ? info.users : [];
          });
        })
        .catch(err => {
          this.variableList = [];
          this.$error(err.msg);
        });
    },
    onSearch() {
      this.getDeployed();
    },
    getVariables() {
      if (!this.selectWorkflow.deploymentId) {
        return;
      }
      fetchVariables
        .execute({
          deploymentId: this.selectWorkflow.deploymentId
        })
        .then(res => {
          this.variableList = res.map(item => {
            if (item.name == "owner" || item.name == "cn_jwis_sharer") {
              item.users = [{ ...this._startUser }];
            } else {
              item.users = [];
            }
            return item;
          });
          this.findAutoRoleAct();
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    fetchWorkflow() {
      let apis = [];
      let p1 = fetchDeployed.execute({ searchKey: this.searchKey });
      apis.push(p1);

      let p2 = findWorkflowApi.execute({
        containerOid: getCookie("tenantOid"),
        modelCode: this.changeDetail.modelDefinition || this.changeDetail.type,
        modelOid:this.changeDetail.oid
      });
      apis.push(p2);

      return Promise.all(apis).then(([deployedWorkflow, modelWorkflow]) => {
        if (modelWorkflow) {
          this.workflowList = deployedWorkflow.filter(
            p => p.id == modelWorkflow.workflowModelId
          );
          this.selectWorkflow = this.workflowList[0];
        } else {
          this.selectWorkflow = deployedWorkflow[0];
          this.workflowList = deployedWorkflow;
        }

        this.workflowImageUrl = `${Jw.gateway}/${
          Jw.workflowMicroServer
        }/workflow/repository/process-definitions/image/byKey?key=${
          this.selectWorkflow.key
        }&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.form-item{
  width: 60%;
}

.formLable{
  margin: 15px 0;
}
.workflow-template-wrap {
  height: 100%;
  display: flex;
  padding: 0 30px;
  .workflow-template-list {
    width: 300px;
    padding-right: 10px;
    overflow: auto;
    .ant-radio-wrapper {
      display: block;
      line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .workflow-template-info {
    flex: 1;
    border-left: 1px solid rgba(30, 32, 42, 0.15);
    color: rgba(30, 32, 42, 0.85);
    overflow: auto;
    padding: 0 2px 0 16px;
    .right-title {
      color: rgba(30, 32, 42, 0.85);
      font-size: 16px;
      font-weight: 700;
      .right-tip {
        font-size: 14px;
        margin-left: 10px;
      }
    }
    .right-process-img {
      margin: 10px 0;
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 5px;
      height: 300px;
      width: 100%;
      > img {
        height: 100%;
        width: 100%;
      }
    }
    .right-temp-wrap {
      margin: 15px 0;
      .ant-select {
        width: 60%;
        margin-top: 8px;
      }
    }
    .right-handler-wrap {
      .handler-item {
        margin-bottom: 16px;
        .item-head {
          margin-bottom: 8px;
          i {
            margin-left: 8px;
          }
        }
        .item-body {
          display: flex;
        }
        .handler-add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          min-width: 32px;
          height: 32px;
          margin-right: 8px;
          background: #f0f7ff;
          border: 1px solid #a4c9fc;
          border-radius: 50%;
          cursor: pointer;
        }
        .handlers {
          display: flex;
          flex-wrap: wrap;
        }
        .handler {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 8px;
          margin: 0 8px 8px 0;
          background: rgba(30, 32, 42, 0.04);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          .close-icon {
            position: absolute;
            top: -13px;
            right: -8px;
            visibility: hidden;
          }
          .avatar {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
            .ant-avatar {
              width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }
          &:hover {
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
            .close-icon {
              visibility: visible;
            }
          }
        }
      }
    }
  }
}
</style>


